import { Navigate } from 'react-router-dom';
import { RouteKey, RoutePathMap } from '../config';
import { TSTRouteObject } from '../tst-router-dom/tst-route';
import { commonCheckPermission } from '../utils/permission';
import IconFont from '@/components/IconFont';
import { Auth } from '@/constants/auth';
import NoPagePermissionError from '@/pages/error/page';

export const dataRoute: TSTRouteObject = {
  key: RouteKey.Data,
  path: RoutePathMap[RouteKey.Data],
  menu: { name: '数据', icon: <IconFont type="icon-shuju" /> },
  handle: {
    breadcrumb: '数据',
  },
  checkPermission: (data) => {
    return commonCheckPermission(data, [Auth['数据']]);
  },
  children: [
    {
      path: '',
      element: <Navigate to={RoutePathMap[RouteKey.DStoreCompletion]} replace />,
    },
    {
      key: RouteKey.DStoreClosed,
      path: RoutePathMap[RouteKey.DStoreClosed],
      menu: {
        name: '临时歇业门店名单',
      },
      handle: {
        breadcrumb: '临时歇业门店名单',
      },
      errorElement: <NoPagePermissionError />,
      lazy: async () => {
        const { default: Component } = await import('@/pages/data/store/closed/index');

        return { Component };
      },
      children: [
        {
          key: RouteKey.DSCDetail,
          path: RoutePathMap[RouteKey.DSCDetail],
          handle: {
            breadcrumb: '歇业门店取消任务明细',
          },
          errorElement: <NoPagePermissionError />,
          lazy: async () => {
            const { default: Component } = await import('@/pages/data/store/closed/detail/index');

            return { Component };
          },
        },
      ],
    },
    {
      key: RouteKey.DSelfCompletion,
      path: RoutePathMap[RouteKey.DSelfCompletion],
      menu: { name: '门店自检实时情况' },
      errorElement: <NoPagePermissionError />,
      handle: {
        breadcrumb: '门店自检实时情况',
        pageTitle: '门店自检实时情况（仅支持查询近一周实时数据）',
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/data/selfCompletion/index');

        return { Component };
      },
    },
    {
      key: RouteKey.DSelfCompletionTactics,
      path: RoutePathMap[RouteKey.DSelfCompletionTactics],
      menu: { name: '门店自检实时情况（新）' },
      errorElement: <NoPagePermissionError />,
      handle: {
        breadcrumb: '门店自检实时情况（新）',
        pageTitle: '门店自检实时情况（新）',
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/data/selfCompletion/tactics');

        return { Component };
      },
    },
    {
      key: RouteKey.DStoreCompletion,
      path: RoutePathMap[RouteKey.DStoreCompletion],
      menu: { name: '门店完成情况' },
      errorElement: <NoPagePermissionError />,
      handle: {
        breadcrumb: '门店完成情况',
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/data/completion/aliDataBoard');

        return { Component };
      },
    },
    {
      key: RouteKey.DDisqualification,
      path: RoutePathMap[RouteKey.DDisqualification],
      menu: { name: '不合格情况' },
      handle: {
        breadcrumb: '不合格情况',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['不合格情况']]);
      },
      lazy: async () => {
        // const { default: Component } = await import('@/pages/data/disqualification/index');
        // const { default: Component } = await import('@/pages/data/disqualification/aliDataBoard');
        const { default: Component } = await import('@/pages/data/disqualification/board');

        return { Component };
      },

      children: [
        {
          key: RouteKey.DDContent,
          path: RoutePathMap[RouteKey.DDContent],
          handle: {
            breadcrumb: '不合格内容',
          },
          checkPermission: (data) => {
            return commonCheckPermission(data, [Auth['不合格情况']]);
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/data/disqualification/content/index');

            return { Component };
          },
        },
      ],
    },
    {
      key: RouteKey.DDisqualificationTactics,
      path: RoutePathMap[RouteKey.DDisqualificationTactics],
      menu: { name: '不合格情况（新）' },
      handle: {
        breadcrumb: '不合格情况（新）',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['不合格情况（新）']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/data/disqualification/tactics');

        return { Component };
      },
      children: [
        {
          key: RouteKey.DDContentTactics,
          path: RoutePathMap[RouteKey.DDContentTactics],
          handle: {
            breadcrumb: '不合格内容（新）',
          },
          checkPermission: (data) => {
            return commonCheckPermission(data, [Auth['不合格情况（新）']]);
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/data/disqualification/tactics/content');

            return { Component };
          },
        },
      ],
    },
    {
      key: RouteKey.DShopSelfSituation,
      path: RoutePathMap[RouteKey.DShopSelfSituation],
      menu: { name: '门店自检情况' },
      handle: {
        breadcrumb: '门店自检情况',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['门店自检情况']]);
      },
      lazy: async () => {
        // const { default: Component } = await import('@/pages/data/situation/index');
        const { default: Component } = await import('@/pages/data/situation/aliDataBoard');

        return { Component };
      },

      children: [
        {
          key: RouteKey.DSelfContent,
          path: RoutePathMap[RouteKey.DSelfContent],
          handle: {
            breadcrumb: '门店不合格内容',
          },
          checkPermission: (data) => {
            return commonCheckPermission(data, [Auth['门店自检情况']]);
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/data/situation/content/index');

            return { Component };
          },
        },
      ],
    },
    {
      key: RouteKey.DInspectorCompletion,
      path: RoutePathMap[RouteKey.DInspectorCompletion],
      menu: { name: '巡检人完成情况' },
      handle: {
        breadcrumb: '巡检人完成情况',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['巡检人完成情况']]);
      },
      lazy: async () => {
        // const { default: Component } = await import('@/pages/data/inspector/index');
        const { default: Component } = await import('@/pages/data/inspector/aliDataBoard');

        return { Component };
      },
    },
    {
      key: RouteKey.DBoard,
      path: RoutePathMap[RouteKey.DBoard],
      menu: { name: '常规巡检看板' },
      handle: {
        breadcrumb: '常规巡检看板',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['常规巡检看板']]);
      },
      lazy: async () => {
        // const { default: Component } = await import('@/pages/data/board/index');
        const { default: Component } = await import('@/pages/data/board/aliDataBoard');

        return { Component };
      },
    },
    {
      key: RouteKey.DTaskPatrol,
      path: RoutePathMap[RouteKey.DTaskPatrol],
      menu: {
        name: '线上稽核',
      },
      handle: {
        breadcrumb: '线上稽核',
      },
      errorElement: <NoPagePermissionError />,
      lazy: async () => {
        const { default: Component } = await import('@/pages/data/patrol/index');

        return { Component };
      },
    },
    {
      key: RouteKey.DReported,
      path: RoutePathMap[RouteKey.DReported],
      menu: {
        name: '提报项数据',
      },
      handle: {
        breadcrumb: '提报项数据',
      },
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['提报项数据']]);
      },
      errorElement: <NoPagePermissionError />,
      lazy: async () => {
        const { default: Component } = await import('@/pages/data/reported/index');

        return { Component };
      },
    },
  ],
};
