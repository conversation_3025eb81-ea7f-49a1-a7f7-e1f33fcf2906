import { RouteKey, RoutePathMap } from '../config';
import { TSTRouteObject } from '../tst-router-dom/tst-route';
import { commonCheckPermission } from '../utils/permission';
import IconFont from '@/components/IconFont';
import { Auth } from '@/constants/auth';

export const reportCenterRoute: TSTRouteObject = {
  key: RouteKey.ReportCenter,
  path: RoutePathMap[RouteKey.ReportCenter],
  menu: { name: '报告中心', icon: <IconFont type="icon-renwu" /> },
  handle: {
    breadcrumb: '报告中心',
  },
  checkPermission: (data) => {
    return commonCheckPermission(data, [Auth['报告中心']]);
  },
  children: [
    {
      key: RouteKey.RCSelf,
      path: RoutePathMap[RouteKey.RCSelf],
      menu: { name: '自检报告' },
      handle: {
        breadcrumb: '自检报告',
      },
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth.策略_自检报告]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/reportCenter/self/index');

        return { Component };
      },
    },
    {
      key: RouteKey.RCRoutine,
      path: RoutePathMap[RouteKey.RCRoutine],
      menu: { name: '常规巡检报告' },
      handle: {
        breadcrumb: '常规巡检报告',
      },
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth.策略_常规巡检报告]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/reportCenter/routine/index');

        return { Component };
      },
      children: [
        {
          key: RouteKey.RCRTrack,
          path: RoutePathMap[RouteKey.RCRTrack],
          handle: {
            breadcrumb: '问题跟踪',
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/reportCenter/routine/track/index');

            return { Component };
          },
        },
      ],
    },
    {
      key: RouteKey.RCRoutineTwoSquare,
      path: RoutePathMap[RouteKey.RCRoutineTwoSquare],
      menu: { name: '常规巡检报告（二方稽核）' },
      handle: {
        breadcrumb: '常规巡检报告（二方稽核）',
      },
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['策略_常规巡检报告（二方稽核）']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/reportCenter/routine/twoSquare');

        return { Component };
      },
    },
    {
      key: RouteKey.RCTutor,
      path: RoutePathMap[RouteKey.RCTutor],
      menu: { name: '食安到店辅导报告' },
      handle: {
        breadcrumb: '食安到店辅导报告',
      },
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth.策略_食安到店辅导报告]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/reportCenter/tutor/index');

        return { Component };
      },
    },
  ],
};
