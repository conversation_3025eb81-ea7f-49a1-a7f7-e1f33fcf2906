import { RouteKey, RoutePathMap } from '../config';
import { TSTRouteObject } from '../tst-router-dom/tst-route';
import { commonCheckPermission } from '../utils/permission';
import IconFont from '@/components/IconFont';
import { Auth } from '@/constants/auth';
import NoPagePermissionError from '@/pages/error/page';

export const recruitRoute: TSTRouteObject = {
  key: RouteKey.RecruitmentAssistant,
  path: RoutePathMap[RouteKey.RecruitmentAssistant],
  menu: { name: '招聘助手', icon: <IconFont type="icon-calendar-user" /> },
  handle: {
    breadcrumb: '招聘助手',
  },
  checkPermission: (data) => {
    return commonCheckPermission(data, [Auth['招聘助手']]);
  },
  children: [
    {
      key: RouteKey.RAEquipment,
      path: RoutePathMap[RouteKey.RAEquipment],
      menu: { name: '设备管理' },
      handle: {
        breadcrumb: '设备管理',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['设备管理']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/recruit/equipment/index');

        return { Component };
      },
    },
    {
      key: RouteKey.RAAccount,
      path: RoutePathMap[RouteKey.RAAccount],
      menu: { name: 'BOSS账户管理' },
      handle: {
        breadcrumb: 'BOSS账户管理',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['BOSS账户管理']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/recruit/account/index');

        return { Component };
      },
    },
    {
      key: RouteKey.RAGreet,
      path: RoutePathMap[RouteKey.RAGreet],
      menu: { name: '岗位打招呼配置' },
      handle: {
        breadcrumb: '岗位打招呼配置',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['岗位打招呼配置']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/recruit/greet/index');

        return { Component };
      },
    },
    {
      key: RouteKey.RACandidate,
      path: RoutePathMap[RouteKey.RACandidate],
      menu: { name: '面试管理' },
      handle: {
        breadcrumb: '面试管理',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['面试管理']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/recruit/candidate/index');

        return { Component };
      },
    },
    {
      key: RouteKey.RACandidateManage,
      path: RoutePathMap[RouteKey.RACandidateManage],
      menu: { name: '候选人管理' },
      handle: {
        breadcrumb: '候选人管理',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['候选人管理']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/recruit/candidate/manage/index');

        return { Component };
      },
    },
    {
      key: RouteKey.RACandidateResource,
      path: RoutePathMap[RouteKey.RACandidateResource],
      menu: { name: '候选人资源管理' },
      handle: {
        breadcrumb: '候选人资源管理',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['候选人资源管理']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/recruit/candidateResource/index');

        return { Component };
      },
    },
    {
      key: RouteKey.RADemandManage,
      path: RoutePathMap[RouteKey.RADemandManage],
      menu: { name: '岗位招聘需求管理' },
      handle: {
        breadcrumb: '岗位招聘需求管理',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['招聘需求管理']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/recruit/demandManage/index');

        return { Component };
      },

      children: [
        {
          key: RouteKey.RADemandDetail,
          path: RoutePathMap[RouteKey.RADemandDetail],
          handle: {
            breadcrumb: '详情',
          },
          checkPermission: (data) => {
            return commonCheckPermission(data, [Auth['招聘需求管理']]);
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/recruit/demandManage/detail/index');

            return { Component };
          },
        },
      ],
    },
    {
      key: RouteKey.RADemandAudit,
      path: RoutePathMap[RouteKey.RADemandAudit],
      menu: { name: '岗位招聘需求审核' },
      handle: {
        breadcrumb: '岗位招聘需求审核',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['招聘需求审核']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/recruit/demandAudit/index');

        return { Component };
      },

      children: [
        {
          key: RouteKey.RADemandAuditDetail,
          path: RoutePathMap[RouteKey.RADemandAuditDetail],
          handle: {
            breadcrumb: '详情',
          },
          checkPermission: (data) => {
            return commonCheckPermission(data, [Auth['招聘需求审核']]);
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/recruit/demandManage/detail/index');

            return { Component };
          },
        },
      ],
    },
    {
      key: RouteKey.RATrack,
      path: RoutePathMap[RouteKey.RATrack],
      menu: { name: '面试跟踪报表' },
      handle: {
        breadcrumb: '面试跟踪报表',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['面试跟踪报表']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/recruit/track/index');

        return { Component };
      },
    },
    {
      key: RouteKey.RARunning,
      path: RoutePathMap[RouteKey.RARunning],
      menu: { name: '账号运行情况' },
      handle: {
        breadcrumb: '账号运行情况',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['账号运行情况']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/recruit/running/index');

        return { Component };
      },
    },
    {
      key: RouteKey.RSStatistic,
      path: RoutePathMap[RouteKey.RSStatistic],
      menu: { name: '招聘数据统计看板' },
      handle: {
        breadcrumb: '招聘数据统计看板',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['招聘数据统计看板']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/recruit/statistic/index');

        return { Component };
      },
    },
    {
      key: RouteKey.RAJobPostingBoard,
      path: RoutePathMap[RouteKey.RAJobPostingBoard],
      menu: { name: '岗位发布看板' },
      handle: {
        breadcrumb: '岗位发布看板',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['招聘助手']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/recruit/jobPostingBoard/index');

        return { Component };
      },
    },
    {
      key: RouteKey.RADifficultStore,
      path: RoutePathMap[RouteKey.RADifficultStore],
      menu: { name: '困难门店识别' },
      handle: {
        breadcrumb: '困难门店识别',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['困难门店识别']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/recruit/difficultStore/index');

        return { Component };
      },
    },
    {
      key: RouteKey.RADataManagement,
      path: RoutePathMap[RouteKey.RADataManagement],
      menu: { name: '人工招聘数据管理' },
      handle: {
        breadcrumb: '人工招聘数据管理',
      },
      errorElement: <NoPagePermissionError />,
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth['人工招聘数据管理']]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/recruit/dataManagement/index');

        return { Component };
      },
    },
  ],
};
