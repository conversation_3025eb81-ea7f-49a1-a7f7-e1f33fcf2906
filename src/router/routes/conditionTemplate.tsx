import { RouteKey, RoutePathMap } from '../config';
import { TSTRouteObject } from '../tst-router-dom/tst-route';
import { commonCheckPermission } from '../utils/permission';
import IconFont from '@/components/IconFont';
import { Auth } from '@/constants/auth';

export const conditionTemplateRoute: TSTRouteObject = {
  key: RouteKey.ConditionTemplate,
  path: RoutePathMap[RouteKey.ConditionTemplate],
  menu: { name: '条件模板中心', icon: <IconFont type="icon-renwu" /> },
  handle: {
    breadcrumb: '条件模板中心',
  },
  checkPermission: (data) => {
    return commonCheckPermission(data, [Auth.条件模板中心]);
  },
  children: [
    {
      key: RouteKey.CTransfer,
      path: RoutePathMap[RouteKey.CTransfer],
      menu: { name: '转派条件模板' },
      handle: {
        breadcrumb: '转派条件模板',
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/conditionTemplate/transfer/index');

        return { Component };
      },
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth.转派条件模板]);
      },
      children: [
        {
          key: RouteKey.CTDetail,
          path: RoutePathMap[RouteKey.CTDetail],
          handle: {
            breadcrumb: '转派条件模板详情',
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/conditionTemplate/transfer/detail/index');

            return { Component };
          },
        },
      ],
    },
    {
      key: RouteKey.CAudit,
      path: RoutePathMap[RouteKey.CAudit],
      menu: { name: '审核条件模板' },
      handle: {
        breadcrumb: '审核条件模板',
      },
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth.审核条件模板]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/conditionTemplate/audit/index');

        return { Component };
      },
      children: [
        {
          key: RouteKey.CAuditDetail,
          path: RoutePathMap[RouteKey.CAuditDetail],
          handle: {
            breadcrumb: '审核条件模板详情',
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/conditionTemplate/audit/detail/index');

            return { Component };
          },
        },
      ],
    },
    {
      key: RouteKey.WMessage,
      path: RoutePathMap[RouteKey.WMessage],
      menu: { name: '预警消息模板' },
      handle: {
        breadcrumb: '预警消息模板',
      },
      checkPermission: (data) => {
        return commonCheckPermission(data, [Auth.预警消息模板]);
      },
      lazy: async () => {
        const { default: Component } = await import('@/pages/conditionTemplate/warningMessage/index');

        return { Component };
      },
      children: [
        {
          key: RouteKey.WMessageDetail,
          path: RoutePathMap[RouteKey.WMessageDetail],
          handle: {
            breadcrumb: '预警消息模板详情',
          },
          lazy: async () => {
            const { default: Component } = await import('@/pages/conditionTemplate/warningMessage/detail/index');

            return { Component };
          },
        },
      ],
    },
  ],
};
