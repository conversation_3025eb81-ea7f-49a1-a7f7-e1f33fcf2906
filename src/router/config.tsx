export enum RouteKey {
  Home = 'Home', // 首页
  Login = 'Login', // 登录页
  Forget = 'Forget', // 忘记密码页
  MediaView = 'MediaView', // 视频查看页

  ServiceParameter = 'ServiceParameter', // 业务参数设置
  SPSop = 'SPSop', // 业务参数设置-SOP(标准作业流程)管理
  SPSTag = 'SPSTag', // 业务参数设置-SOP(标准作业流程)管理-sop标签
  SPSView = 'SPSView', // 业务参数设置-SOP(标准作业流程)管理-查看sop

  SPBasic = 'SPBasic', // 业务参数设置-基础设置
  SPBasicStrategy = 'SPBasicStrategy', // 业务参数设置-基础设置-策略

  SPCheckItem = 'SPCheckItem', // 业务参数设置-检查项管理
  SPCheckItemStrategy = 'SPCheckItemStrategy', // 业务参数设置-检查项管理-策略

  SPReportedItem = 'SPReportedItem', // 业务参数设置-检查项管理

  SPCheckItemAdd = 'SPCheckItemAdd', // 业务参数设置-检查项管理-新增检查项
  SPCheckItemEdit = 'SPCheckItemEdit', // 业务参数设置-检查项管理-编辑检查项
  SPCheckItemType = 'SPCheckItemType', // 业务参数设置-检查项管理-检查项类型

  SPCISDetail = 'SPCISDetail', // 业务参数设置-检查项管理-策略-检查项详情
  SPCISType = 'SPCISType', // 业务参数设置-检查项管理-策略-检查项类型

  SPChecklist = 'SPChecklist', // 业务参数设置-检查表管理
  SPCTag = 'SPCTag', // 业务参数设置-检查表管理-检查表标签
  SPCEdit = 'SPCEdit', // 业务参数设置-检查表管理-编辑检查表
  SPCESelf = 'SPCESelf', // 业务参数设置-检查表管理-编辑检查表-自检检查表
  SPCERoutine = 'SPCERoutine', // 业务参数设置-检查表管理-编辑检查表-巡检检查项
  SPCEDisinfection = 'SPCEDisinfection', // 业务参数设置-检查表管理-编辑检查表-消杀检查项

  SPChecklistStrategy = 'SPChecklistStrategy', // 业务参数设置-检查表管理-策略
  SPCSTag = 'SPCSTag', // 业务参数设置-检查表管理-策略-检查表标签
  SPCSEdit = 'SPCSEdit', // 业务参数设置-检查表管理-策略-编辑检查表
  SPCSESelf = 'SPCSESelf', // 业务参数设置-检查表管理-策略-编辑检查表-自检检查表
  SPCSERoutine = 'SPCSERoutine', // 业务参数设置-检查表管理-策略-编辑检查表-巡检检查项

  SPTaskType = 'SPTaskType', // 业务参数设置-常规任务类型管理
  SStructureKill = 'SStructureKill', // 业务参数设置-消杀公司档案
  SPComplaints = 'SPComplaints', // 业务参数设置-申诉报告设置
  SSMView = 'SSMView', // 消杀公司档案--查看

  SPDiagnosis = 'SPDiagnosis', //  业务参数设置-诊断任务检查表管理
  SPDConfig = 'SPDConfig', //  业务参数设置-诊断任务检查表管理-配置

  SPStoreAuditChecklist = 'SPStoreAuditChecklist', // 业务参数设置-食安稽核到店辅导检查表管理

  SPermissionTagManger = 'SPermissionTagManger', // 业务参数设置-权限标签管理

  Task = 'Task', // 任务
  TSelf = 'TSelf', // 任务-自检任务
  TSOnce = 'TSOnce', // 任务-自检任务-创建或编辑单次
  TSPlan = 'TSPlan', // 任务-自检任务-创建或编辑循环
  TRoutineInspection = 'TRoutineInspection', // 任务-常规巡检任务创建
  TRoutineNormal = 'TRoutineNormal', // 任务-常规巡检任务
  TRIFast = 'TRIFast', // 任务-常规巡检任务创建-快速创建巡检计划
  TRIBatch = 'TRIBatch', // 任务-常规巡检任务创建-批量创建巡检计划
  TROnce = 'TROnce', // 任务-常规巡检任务创建-
  TRLoop = 'TRLoop', // 任务-常规巡检任务创建-

  TAIInspection = 'TAIInspection', // 任务-AI巡检任务创建
  TAIInspectionEdit = 'TAIInspectionEdit', // 任务-AI巡检任务创建 - 编辑
  TAIInspectionReview = 'TAIInspectionReview', // 任务-AI巡检任务创建 - 编辑
  TAIInspectionDetail = 'TAIInspectionDetail', // 任务-AI巡检任务创建 - 详情
  TCloudInspection = 'TCloudInspection', // 任务-视频云巡检任务

  TStandardTaskManage = 'TStandardTaskManage', // 任务-常规任务管理
  TStandardTaskDetail = 'TStandardTaskDetail', // 任务-常规任务详情

  TDisinfection = 'TDisinfection', //  任务-消毒任务管理
  TDDetail = 'TDDetail', //  任务-消毒任务管理-任务详情
  TDSchedulingDetail = 'TDSchedulingDetail', // 任务-消毒任务管理-排班任务详情
  TDSchedulingSubmit = 'TDSchedulingSubmit', // 任务-消毒任务管理-提交排班
  TDPlan = 'TDPlan', // 任务-消毒任务管理-计划配置
  TDiagnosis = 'TDiagnosis', // 任务-诊断任务管理
  TTutor = 'TTutor', // 任务-食安稽核到店辅导任务
  TTDetail = 'TTDetail', // 任务-食安稽核到店辅导任务-任务详情
  TComplaintsPending = 'TComplaintsPending', // 任务-申诉待处理任务
  TAppealDealTask = 'TAppealDealTask', // 任务-申诉处理任务
  TTaskTransfer = 'TTaskTransfer', // 任务-任务转办申请  TComplaintsPending = 'TComplaintsPending', // 任务-申诉待处理任务
  Report = 'Report', // 报告
  RSelf = 'RSelf', // 报告-自检报告
  RSTrack = 'RSTrack', // 报告-自检报告-问题跟踪
  RRoutine = 'RRoutine', // 报告-常规巡检报告
  RRTrack = 'RRTrack', // 报告-常规巡检报告-问题跟踪
  RRCircularRectification = 'RRCircularRectification', // 报告-常规巡检报告-循环整改检查项
  RDisinfection = 'RDisinfection', // 报告-消杀报告
  RDTrack = 'RDTrack', // 报告-消杀报告-问题跟踪
  RTutor = 'RTutor', // 报告-食安到店辅导报告
  RReasonMaintenance = 'RReasonMaintenance', // 报告-不合格原因维护
  RectificationTrack = 'RectificationTrack', // 整改跟踪
  RTSelf = 'RTSelf', // 整改跟踪-自检整改跟踪
  RTRoutine = 'RTRoutine', // 整改跟踪-巡检整改跟踪
  RTAIInspection = 'RTAIInspection', // 整改跟踪-ai巡检整改跟踪
  RTDisinfection = 'RTDisinfection', // 整改跟踪-消杀任务整改跟踪
  RTStudy = 'RTStudy', // 整改跟踪-整改学习明细

  Data = 'Data', // 数据
  DSelfCompletion = 'DSelfCompletion', // 数据-门店自检实时情况
  DSelfCompletionTactics = 'DSelfCompletionTactics', // 数据-门店自检实时情况-策略
  DStoreCompletion = 'DStoreCompletion', // 数据-门店完成情况
  DDisqualification = 'DDisqualification', // 数据-不合格情况
  DDisqualificationTactics = 'DDisqualificationTactics', // 数据-不合格情况-策略
  DDContent = 'DDContent', // 数据-不合格情况-不合格内容
  DDContentTactics = 'DDContentTactics', // 数据-不合格情况-不合格内容-策略
  DShopSelfSituation = 'DShopSelfSituation', // 数据门店自检情况
  DSelfContent = 'DSelfContent', // 数据门店自检不合格内容
  DInspectorCompletion = 'DInspectorCompletion', // 数据-巡检人完成情况
  DTaskPatrol = 'DTaskPatrol', // 数据- 线上稽核
  DShopAISelfChecking = 'DShopAISelfChecking', // 数据-门店Ai自检情况
  DStoreClosed = 'DStoreClosed', // 数据-门店临时闭店
  DSCDetail = 'DSCDetail', // 数据-临时闭店-详情
  DBoard = 'DBoard', // 数据-常规巡检看板
  DReported = 'DReported', // 数据-提报项数据

  EnterpriseManagement = 'EnterpriseManagement', // 企业管理
  EMStoreManagement = 'EMStoreManagement', // 企业管理-门店管理
  EMSMExpire = 'EMSMExpire', // 企业管理-门店管理-证照临期/过期门店
  EMSMAdjust = 'EMSMAdjust', // 企业管理-门店管理-需调整门店
  EMSMView = 'EMSMView', // 企业管理-门店管理-查看
  EMOrganizationalStructure = 'EMOrganizationalStructure', // 企业管理-组织架构

  EMMonitorViewRecord = 'EMMonitorViewRecord', // 企业管理-视频监控查看记录

  DataSource = 'DataSource', // 数据源管理
  DSMarketAnalysis = 'DSMarketAnalysis', // 数据源管理-市场分析数据源管理
  DSMAbrands = 'DSMAbrands', // 数据源管理-市场分析数据源管理-品牌管理
  DSMAimport = 'DSMAimport', // 数据源管理-市场分析数据源管理-批量导入数据
  DSUserSatisfaction = 'DSUserSatisfaction', // 数据源管理-用户满意度数据源管理
  DSUSimport = 'DSUSimport', // 数据源管理-用户满意度数据源管理-批量数据导入

  ReportDownload = 'ReportDownload', // 报表下载
  RDMyExport = 'RDMyExport', // 报表下载-我的导出

  MessageManagement = 'MessageManagement', // 消息管理
  MMTemplate = 'MMTemplate', // 消息管理-消息模板管理
  MMTSetting = 'MMTSetting', // 消息管理-消息模板管理-设置
  MMModule = 'MMModule', // 消息管理-策略消息模块管理
  MMMessageDetail = 'MMMessageDetail', // 消息管理-策略消息模块管理-模板详情
  MMNotice = 'MMNotice', // 消息管理-公告管理
  MMNOperate = 'MMNOperate', // 消息管理-公告管理-操作（新增、编辑）
  MMFeedback = 'MMFeedback', // 消息管理-问题反馈
  MMFView = 'MMFView', // 消息管理-问题反馈-查看

  RecruitmentAssistant = 'RecruitmentAssistant', // 招聘助手
  RAEquipment = 'RAEquipment', // 招聘助手-设备管理
  RAAccount = 'RAAccount', // 招聘助手-BOSS账户管理
  RAMaintainer = 'RAMaintainer', // 招聘助手-账号管理员
  RAGreet = 'RAGreet', // 招聘助手-岗位打招呼配置
  RACandidate = 'RACandidate', // 招聘助手-候选人信息看板
  RACandidateManage = 'RACandidateManage', // 招聘助手-候选人管理
  RACandidateResource = 'RACandidateResource', // 招聘助手-候选人资源管理
  RADemandManage = 'RADemandManage', // 招聘助手-招聘需求管理
  RADemandDetail = 'RADemandDetail', // 招聘助手-招聘需求详情
  RADemandAudit = 'RADemandAudit', // 招聘助手-招聘需求审核
  RADemandAuditDetail = 'RADemandAuditDetail', // 招聘助手-招聘需求审核-详情
  RATrack = 'RATrack', // 招聘助手-面试跟踪报表
  RAJobPostingBoard = 'RAJobPostingBoard', // 招聘助手-岗位发布看板
  RADifficultStore = 'RADifficultStore', // 招聘助手-困难门店识别
  RADataManagement = 'RADataManagement', // 招聘助手-人工招聘数据管理

  Template = 'Template', // 任务模板中心
  TplSelf = 'TplSelf', // 任务模板中心-自检任务模板
  TSDetail = 'TSDetail', // 任务模板中心-自检任务模板-模板详情
  TplRoutine = 'TplRoutine', // 任务模板中心-巡检任务模板
  TRDetail = 'TRDetail', // 任务模板中心-巡检任务模板-模板详情
  TDiagnosisWorkSheetSetting = 'TDiagnosisWorkSheetSetting', // 任务模板中心-巡检任务模板-诊断检查表设置
  TplReview = 'TplReview', // 任务模板中心-点评任务模板
  TRVDetail = 'TRVDetail', // 任务模板中心-点评任务模板-模板详情
  TplRectification = 'TplRectification', // 任务模板中心-整改任务模板
  TRFDetail = 'TRFDetail', // 任务模板中心-整改任务模板-模板详情
  TplAppeal = 'TplAppeal', // 任务模板中心-申诉处理任务
  TplADetail = 'TplADetail', // 任务模板中心-申诉处理任务-模板详情

  ConditionTemplate = 'ConditionTemplate', // 条件模板中心
  CTransfer = 'CTransfer', // 条件模板中心-转派条件模板
  CTDetail = 'CTDetail', // 条件模板中心-转派条件模板-模板详情

  Strategy = 'Strategy', // 策略
  SManage = 'SManage', // 策略-策略管理
  SVideoPatrol = 'SVideoPatrol', // 策略-视频云巡检
  SMConfig = 'SMConfig', // 策略-策略管理-策略配置

  TaskCenter = 'TaskCenter', // 任务中心
  TCList = 'TCList', // 任务中心
  TCLDetail = 'TCLDetail', // 任务中心-任务明细
  TCLDInfo = 'TCLDInfo', // 任务中心
  TCTransfer = 'TCTransfer', // 任务中心-任务转办申请
  TCCloud = 'TCCloud', // 任务中心-视频云巡检
  TCCPatrol = 'TCCPatrol', // 任务中心-视频云巡检-巡检
  TCAppealAwaitDealTask = 'TCAppealAwaitDealTask', // 任务中心-申诉待处理任务
  TCAppealDealTask = 'TCAppealDealTask', // 任务中心-申诉处理任务
  TCIssueFailTaskDetail = 'TCIssueFailTaskDetail', // 任务中心-下发失败任务明细

  ReportCenter = 'ReportCenter', // 报告中心
  RCSelf = 'RCSelf', // 报告中心-自检报告
  RCRoutine = 'RCRoutine', // 报告中心-常规巡检报告
  RCRoutineTwoSquare = 'RCRoutineTwoSquare', // 报告中心-常规巡检报告（二方）
  RCRTrack = 'RCRTrack',
  RCTutor = 'RCTutor', // 报告中心-常规巡检报告

  RectificationStrategy = 'RectificationStrategy', // 整改跟踪-策略
  RSSelf = 'RSSelf', // 整改跟踪-策略-自检整改跟踪
  RSRoutine = 'RSRoutine', // 整改跟踪-策略-常规巡检整改跟踪
  RSStudy = 'RSStudy', // 整改跟踪-策略-整改学习明细
  RARunning = 'RARunning', // 招聘助手-账号运行情况
  RSStatistic = 'RSStatistic', // 招聘助手-招聘数据统计看板
  Invest = 'Invest', // 统投管理
  InvestConfirm = 'InvestConfirm', // 统投确认管理
  InvestConfirmMonthPlan = 'InvestConfirmMonthPlan', // 投流月度计划管理
  InvestConfirmDetail = 'InvestConfirmDetail', // 统投确认管理详情
  InvestConfirmDetailOperationRecord = 'InvestConfirmDetailOperationRecord', // 统投确认管理详情操作记录
  InvestMtResult = 'InvestMtResult', // 美团投流结果查询
  InvestMtResultDetail = 'InvestMtResultDetail', // 美团投流结果查询详情
  InvestElemResult = 'InvestElemResult', // 饿了么投流结果查询
  InvestElemResultDetail = 'InvestElemResultDetail', // 饿了么投流结果查询详情
  Administrator = 'InvestAdministrator', // 管理员配置查询
  InvestMtParamsConfig = 'InvestMtParamsConfig', // 美团投流参数配置
  InvestElmParamsConfig = 'InvestElmParamsConfig', // 美团投流参数配置
  InvestAddPlan = 'InvestAddPlan', // 加投计划管理
  InvestAddPlanCreate = 'InvestAddPlanCreate', // 加投计划创建
  InvestAddPlanEdit = 'InvestAddPlanEdit', // 加投计划编辑
  InvestAddPlanDetail = 'InvestAddPlanDetail', // 加投计划详情

  MyTasks = 'MyTasks', // 我的任务

  /** 我的任务 */
  SelfTask = 'SelfTask',
  ReviewTask = 'ReviewTask',
  ReviewTaskComment = 'ReviewTaskComment', // 点评任务-点评
  ReviewTaskFoodSafe = 'ReviewTaskFoodSafe', // 点评任务-食安稽核到店辅导点评任务
  CounselingReview = 'CounselingReview',

  CAudit = 'CAudit', // 审核条件模板
  CAuditDetail = 'CAuditDetail', // 审核条件模板详情

  WMessage = 'WMessage', // 预警消息模板
  WMessageDetail = 'WMessageDetail', // 预警消息模板详情

  WorkSheetManage = 'WorkSheetManage', // 差异项到店检查表管理
  DifferenceAudit = 'DifferenceAudit', // 差异项到店审核

  /** 异常监控 */
  ErrorSurveillance = 'ErrorSurveillance',
  TaskRelevancyAiStatus = 'TaskRelevancyAiStatus', // 异常监控-任务关联AI状态

  TCTransferNormal = 'TCTransferNormal',
  TCTransferFoodsafe = 'TCTransferFoodsafe',

  /** 任务中心 */
  TCAppealFoodAppeal = 'TCAppealFoodAppeal', // 任务中心-食安申诉任务

  AuditSchedulingManage = 'AuditSchedulingManage', // 稽核排班管理

  FoodSafeErrorReportApplyDeal = 'FoodSafeErrorReportApplyDeal', // 食安异常报备处理

  EventLog = 'EventLog', // 事件日志
  EventLogDetail = 'EventLogDetail', // 事件日志详情
}

export const RoutePathMap: Record<RouteKey, string> = {
  [RouteKey.Home]: '/',
  [RouteKey.Login]: '/login',
  [RouteKey.Forget]: '/forget',
  [RouteKey.MediaView]: '/mediaView',
  [RouteKey.ServiceParameter]: 'parameter',
  [RouteKey.SPSop]: 'sop',
  [RouteKey.SPSTag]: 'tag',
  [RouteKey.SPSView]: 'view',

  [RouteKey.SPBasic]: 'basic',
  [RouteKey.SPBasicStrategy]: 'basic-strategy',

  [RouteKey.SPCheckItem]: 'checkItem',
  [RouteKey.SPCheckItemStrategy]: 'check-item-strategy',
  [RouteKey.SPReportedItem]: 'reported',
  [RouteKey.SPCheckItemAdd]: 'checkItemAdd',
  [RouteKey.SPCheckItemEdit]: 'checkItemEdit',
  [RouteKey.SPCheckItemType]: 'checkItemType',
  [RouteKey.SPCISDetail]: 'detail',
  [RouteKey.SPCISType]: 'type',
  [RouteKey.SPChecklist]: 'checklist',
  [RouteKey.SPChecklistStrategy]: 'checklist-strategy',
  [RouteKey.SPermissionTagManger]: 'permissionTagManger',
  [RouteKey.SPCTag]: 'tag',
  [RouteKey.SPCEdit]: 'edit',
  [RouteKey.SPCESelf]: 'self',
  [RouteKey.SPCERoutine]: 'routine',
  [RouteKey.SPCEDisinfection]: 'disinfection',

  [RouteKey.SPCSTag]: 'tag',
  [RouteKey.SPCSEdit]: 'edit',
  [RouteKey.SPCSESelf]: 'self',
  [RouteKey.SPCSERoutine]: 'routine',

  [RouteKey.SPTaskType]: 'taskType',
  [RouteKey.SStructureKill]: 'structureKill',
  [RouteKey.SPComplaints]: 'SPComplaints',
  [RouteKey.SSMView]: 'view',

  [RouteKey.SPDiagnosis]: 'diagnosis',
  [RouteKey.SPDConfig]: 'config',
  [RouteKey.SPStoreAuditChecklist]: 'audit',

  [RouteKey.Task]: 'task',

  [RouteKey.TSelf]: 'self',
  [RouteKey.TSOnce]: 'once',
  [RouteKey.TSPlan]: 'plan',
  [RouteKey.TRoutineInspection]: 'routine',
  [RouteKey.TRoutineNormal]: 'normal',
  [RouteKey.TRIFast]: 'fast',
  [RouteKey.TRIBatch]: 'batch',
  [RouteKey.TROnce]: 'once',
  [RouteKey.TRLoop]: 'loop',
  [RouteKey.TCloudInspection]: 'cloud',
  [RouteKey.TAIInspection]: 'ai',
  [RouteKey.TAIInspectionEdit]: 'edit',
  [RouteKey.TAIInspectionReview]: 'review',
  [RouteKey.TAIInspectionDetail]: 'detail',
  [RouteKey.TStandardTaskManage]: 'standardTaskManage',
  [RouteKey.TStandardTaskDetail]: 'standardTaskDetail',
  [RouteKey.TDisinfection]: 'disinfection',
  [RouteKey.TDDetail]: 'detail',
  [RouteKey.TDSchedulingDetail]: 'schedulingDetail',
  [RouteKey.TDSchedulingSubmit]: 'schedulingSubmit',
  [RouteKey.TDPlan]: 'TDPlan',
  [RouteKey.TDiagnosis]: 'diagnosis',
  [RouteKey.TTutor]: 'tutor',
  [RouteKey.TTDetail]: 'detail',
  [RouteKey.TComplaintsPending]: 'pending',
  [RouteKey.TAppealDealTask]: 'appealDealTask',
  [RouteKey.TTaskTransfer]: 'transfer',
  [RouteKey.Report]: 'report',
  [RouteKey.RSelf]: 'self',
  [RouteKey.RSTrack]: 'track',
  [RouteKey.RRoutine]: 'routine',
  [RouteKey.RDisinfection]: 'disinfection',
  [RouteKey.RReasonMaintenance]: 'reasonMaintenance',
  [RouteKey.RDTrack]: 'track',
  [RouteKey.RRTrack]: 'track',
  [RouteKey.RRCircularRectification]: 'circularRectification',
  [RouteKey.RectificationTrack]: 'rectification',
  [RouteKey.RTSelf]: 'self',
  [RouteKey.RTRoutine]: 'routine',
  [RouteKey.RTAIInspection]: 'ai',
  [RouteKey.RTDisinfection]: 'disinfection',
  [RouteKey.RTStudy]: 'study',
  [RouteKey.RTutor]: 'tutor',

  [RouteKey.Data]: 'data',
  [RouteKey.DSelfCompletion]: 'selfCompletion',
  [RouteKey.DSelfCompletionTactics]: 'selfCompletionTactics',
  [RouteKey.DStoreCompletion]: 'completion',
  [RouteKey.DDisqualification]: 'disqualification',
  [RouteKey.DDisqualificationTactics]: 'disqualificationTactics',
  [RouteKey.DDContent]: 'content',
  [RouteKey.DDContentTactics]: 'contentTactics',
  [RouteKey.DInspectorCompletion]: 'inspector',
  [RouteKey.DStoreClosed]: 'store',
  [RouteKey.DSCDetail]: 'detail',
  [RouteKey.DBoard]: 'board',
  [RouteKey.DReported]: 'reported',
  [RouteKey.DTaskPatrol]: 'patrol',
  [RouteKey.DShopAISelfChecking]: 'shopAISelfChecking',
  [RouteKey.EnterpriseManagement]: 'enterprise',
  [RouteKey.EMStoreManagement]: 'store',
  [RouteKey.EMSMExpire]: 'expire',
  [RouteKey.EMSMAdjust]: 'adjust',
  [RouteKey.EMSMView]: 'view',
  [RouteKey.EMOrganizationalStructure]: 'structure',

  [RouteKey.EMMonitorViewRecord]: 'monitor',
  [RouteKey.DataSource]: 'source',
  [RouteKey.DSMarketAnalysis]: 'market',
  [RouteKey.DSMAbrands]: 'brands',
  [RouteKey.DSMAimport]: 'import',
  [RouteKey.DSUserSatisfaction]: 'satisfaction',
  [RouteKey.DSUSimport]: 'import',
  [RouteKey.ReportDownload]: 'download',
  [RouteKey.RDMyExport]: 'export',
  [RouteKey.MessageManagement]: 'message',
  [RouteKey.MMTemplate]: 'template',
  [RouteKey.MMTSetting]: 'setting',
  [RouteKey.MMModule]: 'module',
  [RouteKey.MMMessageDetail]: 'messageDetail',
  [RouteKey.MMNotice]: 'notice',
  [RouteKey.MMNOperate]: 'operate',
  [RouteKey.MMFeedback]: 'feedback',
  [RouteKey.MMFView]: 'view',
  [RouteKey.DShopSelfSituation]: 'situation',
  [RouteKey.DSelfContent]: 'selfcontent',

  [RouteKey.RecruitmentAssistant]: 'recruit',
  [RouteKey.RAEquipment]: 'equipment',
  [RouteKey.RAAccount]: 'account',
  [RouteKey.RAMaintainer]: 'maintainer',
  [RouteKey.RAGreet]: 'greet',
  [RouteKey.RACandidate]: 'candidate',
  [RouteKey.RACandidateManage]: 'candidateManage',
  [RouteKey.RACandidateResource]: 'candidateResource',
  [RouteKey.RADemandManage]: 'demandManage',
  [RouteKey.RADemandDetail]: 'demandDetail',
  [RouteKey.RADemandAudit]: 'demandAudit',
  [RouteKey.RADemandAuditDetail]: 'demandAuditDetail',
  [RouteKey.RATrack]: 'track',
  [RouteKey.RAJobPostingBoard]: 'jobPostingBoard',
  [RouteKey.RADifficultStore]: 'difficultStore',
  [RouteKey.RADataManagement]: 'dataManagement',

  [RouteKey.Template]: 'template',
  [RouteKey.TplSelf]: 'self',
  [RouteKey.TplRoutine]: 'routine',
  [RouteKey.TplReview]: 'review',
  [RouteKey.TplRectification]: 'rectification',
  [RouteKey.TplAppeal]: 'appeal',
  [RouteKey.TSDetail]: 'detail',
  [RouteKey.TRDetail]: 'detail',
  [RouteKey.TDiagnosisWorkSheetSetting]: 'workSheetSetting',
  [RouteKey.TRVDetail]: 'detail',
  [RouteKey.TRFDetail]: 'detail',
  [RouteKey.TplADetail]: 'detail',

  [RouteKey.ConditionTemplate]: 'conditiontemplate',
  [RouteKey.CTransfer]: 'transfer',
  [RouteKey.CTDetail]: 'detail',

  [RouteKey.Strategy]: 'strategy',
  [RouteKey.SManage]: 'manage',
  [RouteKey.SVideoPatrol]: 'videoPatrol',
  [RouteKey.SMConfig]: 'config',

  [RouteKey.TaskCenter]: 'taskCenter',
  [RouteKey.TCList]: 'list',
  [RouteKey.TCLDetail]: 'detail',
  [RouteKey.TCCloud]: 'cloud',
  [RouteKey.TCCPatrol]: 'patrol',
  [RouteKey.TCLDInfo]: 'info',
  [RouteKey.TCTransfer]: 'transfer',
  [RouteKey.TCAppealAwaitDealTask]: 'appealAwaitDealTask',
  [RouteKey.TCAppealDealTask]: 'appealDealTask',
  [RouteKey.TCIssueFailTaskDetail]: 'issueFailTaskDetail',

  [RouteKey.ReportCenter]: 'reportCenter',
  [RouteKey.RCSelf]: 'self',
  [RouteKey.RCRoutine]: 'routine',
  [RouteKey.RCRoutineTwoSquare]: 'routineTwoSquare',
  [RouteKey.RCRTrack]: 'track',
  [RouteKey.RCTutor]: 'tutor',

  [RouteKey.RectificationStrategy]: 'rectificationStrategy',
  [RouteKey.RSSelf]: 'self',
  [RouteKey.RSRoutine]: 'routine',
  [RouteKey.RSStudy]: 'study',
  // 统投管理
  [RouteKey.Invest]: 'invest',
  [RouteKey.InvestConfirm]: 'confirm',
  [RouteKey.InvestConfirmMonthPlan]: 'monthPlan',
  [RouteKey.InvestConfirmDetail]: 'detail',
  [RouteKey.InvestConfirmDetailOperationRecord]: 'operationRecord',
  [RouteKey.InvestMtResult]: 'mtResult',
  [RouteKey.InvestMtResultDetail]: 'mtResultDetail',
  [RouteKey.InvestElemResult]: 'elemResult',
  [RouteKey.InvestElemResultDetail]: 'elemResultDetail',
  [RouteKey.RARunning]: 'running',
  [RouteKey.RSStatistic]: 'statistic',
  [RouteKey.Administrator]: 'administrator',
  [RouteKey.InvestMtParamsConfig]: 'investMtParamsConfig',
  [RouteKey.InvestElmParamsConfig]: 'investElmParamsConfig',
  [RouteKey.InvestAddPlan]: 'addPlan',
  [RouteKey.InvestAddPlanCreate]: 'addPlanCreate',
  [RouteKey.InvestAddPlanEdit]: 'addPlanEdit',
  [RouteKey.InvestAddPlanDetail]: 'addPlanDetail',

  [RouteKey.MyTasks]: 'myTasks',

  // 我的任务
  [RouteKey.SelfTask]: 'selfTask',
  [RouteKey.ReviewTask]: 'ReviewTask',
  [RouteKey.ReviewTaskComment]: 'reviewTaskComment',
  [RouteKey.ReviewTaskFoodSafe]: 'reviewTaskFoodSafe',
  [RouteKey.CounselingReview]: 'counselingReview',

  [RouteKey.CAudit]: 'audit',
  [RouteKey.CAuditDetail]: 'auditDetail',

  [RouteKey.WorkSheetManage]: 'workSheetManage',
  [RouteKey.DifferenceAudit]: 'differenceAudit',

  // 异常监控
  [RouteKey.ErrorSurveillance]: 'errorSurveillance',
  [RouteKey.TaskRelevancyAiStatus]: 'taskRelevancyAiStatus',

  [RouteKey.TCTransferNormal]: 'TCTransferNormal',
  [RouteKey.TCTransferFoodsafe]: 'TCTransferFoodsafe',

  [RouteKey.TCAppealFoodAppeal]: 'foodAppeal',

  [RouteKey.AuditSchedulingManage]: 'auditSchedulingManage',
  [RouteKey.FoodSafeErrorReportApplyDeal]: 'foodSafeErrorReportApplyDeal',
  [RouteKey.EventLog]: 'eventLog',
  [RouteKey.EventLogDetail]: 'eventLogDetail',

  [RouteKey.WMessage]: 'wMessage',
  [RouteKey.WMessageDetail]: 'WMessageDetail',
};
