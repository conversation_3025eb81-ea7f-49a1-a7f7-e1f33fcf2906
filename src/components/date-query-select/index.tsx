import { cloneElement, FC, useMemo } from 'react';
import { DatePicker, DatePickerProps, Select, SelectProps } from 'antd';
import classnames from 'classnames';
import dayjs from 'dayjs';
import LimitRangePicker from '../limit-range-picker';
import NatureWeek from '../nature-week';

const { RangePicker } = DatePicker;

export interface DateQuerySelectProps {
  typeOptions?: SelectProps['options'];
  limit?: number;
  /** 是否显示默认开始巡检时间选项 默认不展示 */
  showDefaultBeginPatrolTimeOption?: boolean;
  disabledDate?: DatePickerProps['disabledDate'];
  onChange?: (value: any) => void;
  value?: any;
}

export enum DateQueryType {
  TaskDeliveryDate = 1,
  ReportSubmissionTime = 1 << 1,
  BeginpatrolDate = 1 << 2,
}

const DateQueryTypeCN: Record<DateQueryType, string> = {
  [DateQueryType.TaskDeliveryDate]: '任务下发日期',
  [DateQueryType.ReportSubmissionTime]: '报告提交时间',
  [DateQueryType.BeginpatrolDate]: '开始巡检时间',
};

export enum DateType {
  Yesterday = 1 << 3,
  Today = 1 << 4,
  NatureWeek = 1 << 5,
  Custom = 1 << 6,
}

const DateTypeCN: Record<DateType, string> = {
  [DateType.Yesterday]: '昨天',
  [DateType.Today]: '今天',
  [DateType.NatureWeek]: '自然周',
  [DateType.Custom]: '自定义',
};
const dateQueryTypeOptions: any = Object.keys(DateQueryTypeCN).map((key: unknown) => ({
  value: Number(key),
  label: DateQueryTypeCN[key as DateQueryType],
}));
const dateTypeOptions = Object.keys(DateTypeCN).map((key: unknown) => ({
  value: Number(key),
  label: DateTypeCN[key as DateType],
}));

const DateQuerySelect: FC<DateQuerySelectProps> = ({
  typeOptions,
  limit,
  onChange,
  disabledDate,
  showDefaultBeginPatrolTimeOption,
  value: propValue = {
    type: DateQueryType.TaskDeliveryDate,
    dateType: DateType.Yesterday,
    date: dayjs().add(-1, 'days'),
  },
}) => {
  const value = useMemo(() => {
    return propValue;
  }, [propValue]);

  const PickerMap: any = {
    [DateQueryType.TaskDeliveryDate | DateType.Yesterday]: <DatePicker disabled />,
    [DateQueryType.TaskDeliveryDate | DateType.Today]: <DatePicker disabled />,
    [DateQueryType.TaskDeliveryDate | DateType.NatureWeek]: <NatureWeek />,
    [DateQueryType.TaskDeliveryDate | DateType.Custom]: <LimitRangePicker limit={limit || 89} allowClear={false} />,
    [DateQueryType.ReportSubmissionTime]: (
      <RangePicker
        allowClear={false}
        showTime={{
          format: 'HH:mm',
          defaultValue: [dayjs('00:00', 'HH:mm'), dayjs('23:59', 'HH:mm')],
        }}
        format="YYYY-MM-DD HH:mm"
        disabledDate={(current, { from }) => {
          if (limit) {
            if (from) {
              return Math.abs(current.diff(from, 'days')) >= limit;
            }

            return false;
          }
        }}
      />
      // <LimitRangePicker
      //   limit={89}
      //   allowClear={false}
      //   showTime={{
      //     format: "HH:mm",
      //     defaultValue: [dayjs("00:00", "HH:mm"), dayjs("23:59", "HH:mm")],
      //   }}
      //   format="YYYY-MM-DD HH:mm"
      // />
    ),
    [DateQueryType.BeginpatrolDate]: (
      <RangePicker
        allowClear={false}
        showTime={{
          format: 'HH:mm',
          defaultValue: [dayjs('00:00', 'HH:mm'), dayjs('23:59', 'HH:mm')],
        }}
        format="YYYY-MM-DD HH:mm"
        disabledDate={(current, { from }) => {
          if (limit) {
            if (from) {
              return Math.abs(current.diff(from, 'days')) >= limit;
            }

            return false;
          }
        }}
      />
    ),
  };
  const defaultOptions = useMemo(() => {
    if (showDefaultBeginPatrolTimeOption) {
      return dateQueryTypeOptions;
    } else {
      return dateQueryTypeOptions.filter((f) => f?.value !== DateQueryType.BeginpatrolDate);
    }
  }, [showDefaultBeginPatrolTimeOption]);

  return (
    <div className={classnames('flex')}>
      <Select
        options={typeOptions || defaultOptions}
        style={{ width: 128 }}
        value={value?.type}
        onChange={(val) => {
          if (+val & DateQueryType.TaskDeliveryDate) {
            onChange?.({
              date: dayjs().add(-1, 'days'),
              type: val,
              dateType: DateType.Yesterday,
            });
          } else {
            onChange?.({
              date: [dayjs().add(-6, 'days').startOf('day'), dayjs().endOf('day')],
              type: val,
              dateType: undefined,
            });
          }
        }}
      />
      {!!(value?.type & DateQueryType.TaskDeliveryDate) && (
        <Select
          options={dateTypeOptions}
          style={{ width: 88 }}
          value={value?.dateType}
          onChange={(val) => {
            let date: any;

            switch (val) {
              case DateType.Yesterday:
                date = dayjs().add(-1, 'days');

                break;
              case DateType.Today:
                date = dayjs();

                break;
              case DateType.NatureWeek:
                date = [dayjs().startOf('week'), dayjs().endOf('week')];

                break;
              default:
                break;
            }

            onChange?.({ date, type: value.type, dateType: val });
          }}
        />
      )}
      {PickerMap[+value?.type | +value?.dateType] &&
        cloneElement(PickerMap[+value?.type | +value?.dateType], {
          value: value?.date,
          disabledDate,
          onChange: (val: any) => {
            onChange?.({ ...value, date: val });
          },
        })}
    </div>
  );
};

export default DateQuerySelect;
