import { get, post } from '..';
import { URLRequestPrefix } from '../config';

export type TDemandManageData = {
  deviceId?: string;
  nickname?: string;
  phone?: string;
  /**
   * SYSTEM :SYSTEM
   * SHOP :SHOP
   * CORP :CORP
   * DISINFECTION :DISINFECTION
   */
  roleType?: string;
  userId?: number;
  username?: string;
  tab?: 'RECRUIT_CONFIRM' | 'ALL';
};

export interface IDemandListItem {
  id: number;
  shopId: string;
  shopName: string;
  shopType: number;
  shopStatus: number;
  province: string;
  city: string;
  district: string;
  address: string;
  shopOwnerId?: any;
  shopOwnerName?: string;
  privilege: string;
  jobType: string;
  fullTime: boolean;
  requireNum: number;
  ageRange: string;
  gender: number;
  workTimeMorningStart: string;
  workTimeMorningEnd: string;
  workTimeAfternoonStart: string;
  workTimeAfternoonEnd: string;
  workTimeEveningStart: string;
  workTimeEveningEnd: string;
  workTime: string;
  monthRest: number;
  salaryType: string;
  hourSalaryStart: number;
  hourSalaryEnd: number;
  monthSalaryMin: number;
  monthSalaryMax: number;
  allowance: string;
  salaryDesc: string;
  houseInclude: boolean;
  accommodationsDesc: string;
  mealAllowance: string;
  fullAttendenceReward: string;
  salary: string;
  recruitUserId: number;
  recruitUserName: string;
  recruitTimeStart: string;
  recruitTimeEnd: string;
  createUserId: number;
  createUserName: string;
  submitTime?: any;
  createTime: string;
  updateUserId: number;
  updateUserName: string;
  updateTime: string;
  status: string;
}

interface PageRes<T> {
  pages: number;
  total: number;
  data: T[];
}

/** 获取需管理求列表 */
export function getDemandManageList(data: TDemandManageData) {
  return post<PageRes<IDemandListItem>>('/rm-api/corp/shop-recruit/demand-manage', data);
}

/** 获取需管理求列表 */
export function exportDemandManageList(data: TDemandManageData) {
  return post<PageRes<IDemandListItem>>('/rm-api/corp/shop-recruit/demand-manage/export', data);
}

/** 批量确认招聘 */
export function batchConfirmRecruit(data: { idList: number[] }) {
  return post('/rm-api/corp/shop-recruit/confirm-batch', data);
}

/** 批量配置招聘优先级 */
export function batchConfigPriority(data: { idList: number[]; privilege: 'NORMAL' | 'EMERGENCY' }[]) {
  return post('/rm-api/corp/shop-recruit/privilege-batch', data);
}

/** 批量招聘中止、完成 */
export function batchRecruitFinish(data: {
  idList: number[];
  status: 'SUSPENDED' | 'COMPLETED';
  suspendRemark?: string;
}) {
  return post('/rm-api/common/shop-recruit/finish-batch', data);
}

/** 获取招聘需求详情 */
export function getRecruitDemandDetail(params: { id: number }) {
  return get('/rm-api/common/shop-recruit/detail', { params }) as unknown as Promise<IDemandListItem>;
}
export function getProvinceCityArea() {
  return post(`${URLRequestPrefix.Center}/getProvinceCityArea`);
}

export enum RecruitStatusEnum {
  通过 = 'PASS',
  不通过 = 'NO_PASS',
  驳回 = 'REJECT',
}

export enum ActionEnum {
  未知 = 'UNKOWN',
  提交 = 'SUBMIT',
  重新提交 = 'RESUBMIT',
  招聘组 = 'HR',
  运营大狮兄姐 = 'SUPERVISION',
  运营经理 = 'OPERATION_MANAGER',
  确认招聘 = 'RECRUIT_CONFIRM',
  招聘中 = 'RECRUITING',
  撤销 = 'CANCEL',
  中止招聘 = 'SUSPENDED',
  确认完成招聘 = 'COMPLETED',
  删除 = 'DELETED',
  撤回 = 'REVOKE',
}

interface IProcessListItem {
  id: number;
  userId: number;
  name: string;
  action: ActionEnum;
  status?: RecruitStatusEnum;
  createTime: string;
  auditComment?: string;
  remark?: string;
}

/** 获取招聘需求流程记录 */
export function getRecruitProcessRecord(params: { id: number }) {
  return get('/rm-api/common/shop-recruit/process-log', { params }) as unknown as Promise<IProcessListItem[]>;
}

/** 获取招聘审核列表 */
export function getRecruitAuditList(data: TDemandManageData) {
  return post<PageRes<IDemandListItem>>('/rm-api/corp/shop-recruit/list', data);
}

/** 获取是否有下一流转节点 */
export function getHasNext(params: { id: number }) {
  return get('/rm-api/common/shop-recruit/has-next', { params }) as unknown as Promise<{
    hasNext: boolean;
    hasReject: boolean;
    nextFlowableAuditUserId: number;
    nextFlowableAuditUserName: string;
  }>;
}

/** 招聘审核 */
export function recruitAudit(data: {
  idList: number[];
  status: string;
  auditComment: string;
  remark: string;
  nextAuditUserId: number;
}) {
  return post('/rm-api/corp/shop-recruit/audit-batch', data);
}

/** 获取角色id 列表*/
export function getStaticRoleMaps() {
  return get('/rm-api/common/getStaticRoleMaps') as unknown as Promise<{
    supervisionRoleIdList: number[];
    operationManagerRoleIdList: number[];
  }>;
}

interface IUserListItem {
  userId: number;
  username: string;
  nickname: string;
  phone: string;
  alive: number;
  isOpen: number;
  faceRecognitionPictureUrl?: string;
}

/** 获取用户列表 */
export function getUserList(data: { shopId: string; roleIds: number[] }) {
  return post('/rm-api/common/findUsersByRole', data) as unknown as Promise<IUserListItem[]>;
}

type TCandidateData = {
  city: string;
  shopId: string;
  shopName: string;
  jobType: string;
  candidateName: string;
  candidatePhone: string;
  fullTime: boolean;
  activted: boolean;
  pageNo: number;
  pageSize: number;
  startTime: string;
  endTime: string;
};

export interface ICandidateListItem {
  candidateId: number;
  candidateName: string;
  replied: boolean;
  isInterview: boolean;
  loginAccountId: string;
  loginAccountName: string;
  jobId: string;
  jobName: string;
  expectLocationName: string;
  expectDistrictName: string;
  expectShop: string;
  expectShopName: string;
  jobType: string;
  fullTime: boolean;
  candidateRealName: string;
  phone: string;
  activted: boolean;
  salary: string;
  geekGender: number;
  ageDesc: string;
  degreeName: string;
  expectPositionName: string;
  school: string;
  major: string;
  createTime: string;
  updateTime: string;
  updateBy: string;
}

/** 获取候选人列表 */
export function getCandidateList(data: TCandidateData) {
  return post('/rm-api/candidate/list', data) as unknown as Promise<ICandidateListItem[]>;
}

/** 编辑候选人 */
export function updatePhone(data: { id: string; phone: string }) {
  return post(`${URLRequestPrefix.RM}/candidate/updatePhone`, data);
}

// 驳回招聘需求
export const rejectRecruit = async (data) => await post(`${URLRequestPrefix.RM}/corp/shop-recruit/rejectByHr`, data);

type TInterviewData = {
  candidateName: string;
  candidatePhone: string;
  gender: number;
  age: number;
  degreeName: string;
  wxNo: string;
  expectPositionName: string;
};

/**
 * @description 新增面试
 */
export function addInterview(data: TInterviewData) {
  return post(`${URLRequestPrefix.RM}/corp/shop-recruit/manual`, data);
}

/**
 * @description 下载模版链接
 * @returns
 */
export function downloadStencilLink() {
  return post(`${URLRequestPrefix.RM}/shop-recruit-candidate/getTemplateUrl`, undefined, {
    responseType: 'blob',
  });
}

/**
 * @description 提交面试Excel表格
 * @param data
 * @returns
 */
export function submitInterviewExcel(data) {
  return post(`${URLRequestPrefix.RM}/shop-recruit-candidate/submitExcel`, data);
}

/**
 * @description 修改招聘需求
 * @param data
 * @returns
 */
export function modifyRecruit(data) {
  return post(`${URLRequestPrefix.RM}/common/shop-recruit/single-update`, data);
}
