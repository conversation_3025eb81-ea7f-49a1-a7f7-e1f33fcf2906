import { get, post } from '..';
import { URLRequestPrefix } from '../config';
import { StrategyTaskType } from '@/constants/strategy';

export type GetStrategyManageListParams = {
  id?: number;
  strategyName?: string;
  enable?: boolean;
  taskType?: StrategyTaskType;
  pageNo: number;
  pageSize: number;
};
export const getStrategyManageList = async (data: GetStrategyManageListParams) =>
  await post(`${URLRequestPrefix.TM}/corp/strategy/manage/pageList`, data);

export const createStrategy = async (data: { strategyName: string }) =>
  await post(`${URLRequestPrefix.TM}/corp/strategy/manage/add`, data);

export const updateStrategy = async (data: { strategyName: string; id: number; enable?: boolean; bodyJson?: string }) =>
  await post(`${URLRequestPrefix.TM}/corp/strategy/manage/update`, data);

export const deleteStrategy = async (id: number) =>
  await post(`${URLRequestPrefix.TM}/corp/strategy/manage/delete`, { id });

// 获取配置表信息
export const getStrategyConfig = async (taskKind: 'BASE' | 'REVIEW' | 'ISSUE') =>
  await post(`${URLRequestPrefix.TM}/corp/strategy/manage/config/table`, { taskKind });

// 获取策略详情
export const getStrategyDetail = async (id: number) =>
  await post(`${URLRequestPrefix.TM}/corp/strategy/manage/getInfo`, { id });

// 配置策略
export const configStrategy = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/strategy/manage/config`, data);

// 获取策略转派条件模板
export const getTransferTemplateList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/template-transfer/querySimpleTemplateList`, data);

// 获取差异项到店任务模板
export const getDifferenceItemArriveShopTemplateList = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/template-difference-audit/querySimpleTemplateList`, data);

// 获取导入模板下载地址
export const getTemplateDownloadUrl = async () =>
  await post(`${URLRequestPrefix.TM}/corp/strategy/manage/getTemplateUrl`, undefined, {
    responseType: 'blob',
  });

// 上传excel
export const submitExcel = async (data: any) =>
  await post(`${URLRequestPrefix.TM}/corp/strategy/manage/submitExcel`, data);

export interface IOperationRecordListItem {
  taskId: number;
  operateAction: string;
  operatorName: string;
  remark?: string;
  imageResources?: any[];
  createTime: string;
  updateTime: string;
  userChangedAdditionalInfo?: any;
}

// 获取报告操作记录
export const getStragegyOperationLog = async (id: number) =>
  (await get(`${URLRequestPrefix.TM}/common/report/operation-log`, {
    params: { id },
  })) as unknown as IOperationRecordListItem[];
// 获取差异项列表
export const getCheckItemGroupByCondition = async () =>
  await post(`${URLRequestPrefix.TM}/corp/association/check-item/list-by-condition`, {
    differentItem: true,
  });

// 保存提报项
export interface ReportedItemParams {
  /**
   * 上传相册图片类型：跟随系统/允许/不允许
   */
  allowAlbum?: string;
  /**
   * 一级标题处理动作
   */
  firstActionType?: string;
  /**
   * 一级标题
   */
  firstTitle?: string;
  /**
   * id
   */
  id?: number;
  /**
   * 正常按钮名称
   */
  normalButtonName?: string;
  /**
   * 是否拍照
   */
  photoMark?: boolean;
  /**
   * 自定义文案
   */
  remark?: string;
  /**
   * 二级内容列表
   */
  secondActionContent?: string[];
  /**
   * 二级处理动作
   */
  secondActionType?: string;
  /**
   * 二级标题
   */
  secondTitle?: string;
  /**
   * 关联sop
   */
  sopId?: number;
  /**
   * 非正常按钮名称
   */
  unqualifiedButtonName?: string;
  [property: string]: any;
}
export const saveReportedItem = async (data: ReportedItemParams) =>
  await post(`${URLRequestPrefix.TM}/corp/report/item/config/save`, data);

// 提报项详情
export const getReportedItem = async (id: number) =>
  await get(`${URLRequestPrefix.TM}/common/report/item/config/detail`, { params: { id } });

// 获取提报项列表
export interface GetReportedItemListParams {
  /**
   * 提报结束时间
   */
  endDate: string;
  /**
   * 组织id
   */
  groupId?: number;
  pageNo: number;
  pageSize: number;
  /**
   * 二级文案选择的内容
   */
  secondActionSelected?: string;
  /**
   * 门店id
   */
  shopIds?: string[];
  /**
   * 提报开始时间
   */
  startDate: string;
  /**
   * 巡检类型
   */
  taskSubType?: string;
  [property: string]: any;
}
export const getReportedItemList = async (data: GetReportedItemListParams) =>
  await post(`${URLRequestPrefix.TM}/corp/report/item/list`, data);

// 导出提报项列表
export const exportReportedItemList = async (data: GetReportedItemListParams) =>
  await post(`${URLRequestPrefix.TM}/corp/report/item/list/export`, data);

export interface SecondReportItem {
  /**
   * 提报的图片
   */
  itemImages?: string[];
  /**
   * 备注
   */
  remark?: string;
  /**
   * 二级文案选择的内容
   */
  secondActionSelected?: string;
  [property: string]: any;
}
export interface SecondReportItemParams {
  /**
   * 基础任务id
   */
  baseTaskId: number;
  /**
   * 配置id
   */
  configId: number;
  /**
   * 一级标题
   * 一级标题选中0不合格1合格
   */
  firstActionJudge?: boolean;
  /**
   * 二级标题提报的内容
   */
  secondReportItems?: SecondReportItem[];
  [property: string]: any;
}

// 提报项提交
export const submitReportedItem = async (data: SecondReportItemParams) =>
  await post(`${URLRequestPrefix.TM}/common/report/item/save`, data);
