export enum Auth {
  '业务参数设置' = 'serviceParameter',
  'SOP（标准作业流程）管理' = 'serviceParameter:sop',
  'SOP新增' = 'serviceParameter:sop:add',
  'SOP修改' = 'serviceParameter:sop:modify',
  'SOP删除' = 'serviceParameter:sop:del',
  'SOP查看' = 'serviceParameter:sop:see',
  'SOP发布' = 'serviceParameter:sop:push',
  'SOP分类管理' = 'serviceParameter:sop:manage',
  'SOP标签管理' = 'serviceParameter:sop:tagManage',
  '批量设置' = 'serviceParameter:sop:batchSettings',
  '基础设置' = 'serviceParameter:basicSettings',
  '检查项管理' = 'serviceParameter:checkItem',
  '修改稽核任务原因维护' = 'serviceParameter:audit:task:reason',
  '修改稽核任务原因维护_新增' = 'serviceParameter:audit:task:reason:add',
  '修改稽核任务原因维护_编辑' = 'serviceParameter:audit:task:reason:edit',
  '修改稽核任务原因维护_禁/启用' = 'serviceParameter:audit:task:reason:disabled:or:enable',

  '提报项管理' = 'serviceParameter:reportItem',
  '检查表设置' = 'serviceParameter:checklist',
  '权限标签管理' = 'serviceParameter:permissionTag',
  '编辑检查表' = 'serviceParameter:checklist:edit',
  '批量编辑检查表' = 'serviceParameter:checklist:batchEdit',
  '检查表标签' = 'serviceParameter:checklist:tag',
  '新增检查表' = 'serviceParameter:checklist:add',
  '常规任务类型管理' = 'serviceParameter:taskType',
  '诊断任务检查表管理' = 'serviceParameter:diagnosis',
  '消杀公司档案' = 'serviceParameter:structureKill',
  '食安稽核到店辅导检查表设置' = 'serviceParameter:audit',
  '申诉报告设置' = 'serviceParameter:complaints',
  '任务' = 'task',
  '自检任务管理' = 'task:selfTaskManage',
  '自检任务' = 'task:selfTaskManage:task',
  '自检任务_批次' = 'task:selfTaskManage:task:batches',
  '创建自检任务' = 'task:selfTaskManage:task:loop',
  '常规巡检计划管理' = 'task:routineTaskManage',
  '快速创建巡检计划' = 'task:routineTaskManage:fast',
  '批量创建巡检计划' = 'task:routineTaskManage:batchCreate',
  '视频云巡检任务' = 'task:videoTaskManage',
  '创建任务' = 'task:videoTaskManage:create',
  '批量创建任务' = 'task:videoTaskManage:batch',
  '批量开始巡检' = 'task:videoTaskManage:begin',
  '修改通道名称' = 'task:videoTaskManage:modifyChannels',
  'AI巡检任务' = 'task:aiTaskManage',
  'AI图片分析任务' = 'task:aiTaskManage:analysis',
  '审核' = 'task:aiTaskManage:analysis:auditing',
  '编辑' = 'task:aiTaskManage:analysis:modify',
  '常规任务管理' = 'task:standardTaskManage',
  '常规任务管理导出' = 'task:standardTaskManage:export',
  '诊断任务管理' = 'task:diagnosis',
  '诊断任务管理_撤回' = 'task:diagnosis:revoke',
  '食安稽核到店辅导任务' = 'task:tutor',
  '申诉待处理任务' = 'task:complaints:pending',
  '申诉处理任务' = 'task:appeal:deal:task',
  '申诉待处理任务_查看' = 'task:complaints:pending:view',
  '申诉待处理任务_审核' = 'task:complaints:pending:audit',
  '诊断任务权限外转派' = 'diagnosis:permission:out:transfer',
  '到店巡检权限外转派' = 'inspection:permission:out:transfer',

  '消杀任务管理' = 'task:disinfection',
  '消杀任务管理_分配' = 'task:disinfection:assign',
  '消杀任务管理_紧急_分配' = 'task:disinfection:emergency:assign',
  '消杀任务管理_紧急_驳回' = 'task:disinfection:emergency:reject',
  '任务转办申请' = 'task:transfer:apply',
  '报告' = 'report',
  '自检报告' = 'report:self',
  '报告_问题跟踪' = 'report:self:following',
  '常规巡检报告' = 'report:routineInspection',
  '不合格原因维护' = 'report:reasonMaintenance',
  '巡检报告' = 'report:routineInspection:report',
  '问题跟踪' = 'report:routineInspection:report:following',
  '巡检点评' = 'report:routineInspection:report:comment',
  '常规巡检报告_食安点评' = 'report:routineInspection:report:auditComment',
  '消杀报告' = 'report:disinfection',
  '消杀报告_导出' = 'report:disinfection:export',
  '食安到店辅导报告' = 'report:tutor',
  '稽核确认' = 'report:tutor:audit',
  '食安到店辅导报告_点评' = 'report:tutor:review',

  '整改跟踪' = 'rectificationTracking',
  '整改跟踪_自检整改跟踪' = 'rectificationTracking:self',
  '整改跟踪_常规巡检整改跟踪' = 'rectificationTracking:routine',
  '整改跟踪_AI巡检整改跟踪' = 'rectificationTracking:ai',
  '消杀任务整改跟踪' = 'rectificationTracking:disinfection',
  '整改学习明细' = 'rectificationTracking:study',
  '整改跟踪_自检' = 'rectificationTracking:self',
  '整改跟踪_导出' = 'rectificationTracking:self:export',
  '常规巡检' = 'rectificationTracking:routine',
  '常规巡检_导出' = 'rectificationTracking:routine:export',
  'ai巡检' = 'rectificationTracking:ai',
  'ai巡检_导出' = 'rectificationTracking:ai:export',
  '数据' = 'data',
  '门店完成情况' = 'data:complete',
  '门店自检完成情况' = 'data:self:complete',
  '自检' = 'data:complete:self',
  '巡检' = 'data:complete:inspection',
  '诊断' = 'data:complete:diagnosis',
  '不合格情况' = 'data:unqualified',
  '不合格情况（新）' = 'data:unqualified:tactics',
  '不合格情况（新）_导出' = 'data:unqualified:tactics:export',
  '门店自检情况' = 'data:shopSelfSituation',
  '自检不合格情况' = 'data:unqualified:self',
  '自检不合格情况_导出' = 'data:unqualified:self:export',
  '巡检不合格情况' = 'data:unqualified:inspection',
  '导出' = 'data:unqualified:inspection:export',
  '巡检人完成情况' = 'data:inspectorComplete',
  '临时歇业门店名单' = 'data:storeClosed',
  '常规巡检看板' = 'data:board',
  '提报项数据' = 'data:reportItem',
  '企业管理' = 'businessManagement',
  '门店管理' = 'businessManagement:shop',
  '查看定位不准确门店' = 'businessManagement:shop:adjust',
  '组织架构' = 'businessManagement:organizational',
  '视频监控查看记录' = 'businessManagement:record',
  '数据源管理' = 'dataSource',
  '市场分析数据源管理' = 'dataSource:marketAnalysis',
  '市场分析数据源管理_批量导入数据' = 'dataSource:marketAnalysis:import',
  '品牌管理' = 'dataSource:marketAnalysis:brand',
  '品牌管理_批量导入数据' = 'dataSource:marketAnalysis:brand:import',
  '用户满意度数据源管理' = 'dataSource:userSatisfaction',
  '批量导入数据' = 'dataSource:userSatisfaction:import',
  '全渠道投诉数据' = 'dataSource:userSatisfaction:complaint',
  '差评回复率数据' = 'dataSource:userSatisfaction:complaintRes',
  '平均出餐时长数据' = 'dataSource:userSatisfaction:averageTime',
  '顾客体验星级数据' = 'dataSource:userSatisfaction:star',
  '出餐时长超时数据' = 'dataSource:userSatisfaction:overtime',
  '报表下载' = 'reportFormsDownload',
  '我的导出' = 'reportFormsDownload:export',
  '消息管理' = 'messageTemplate',
  '消息模板管理' = 'messageTemplate:',
  '公告管理' = 'messageTemplate:announcement',
  '问题反馈' = 'messageTemplate:feedback',
  //
  '到店巡检' = 'task:routineTaskManage:fast:normal',
  '视频云巡检' = 'task:routineTaskManage:fast:video',
  '食安线下稽核' = 'task:routineTaskManage:fast:FOOD_SAFETY_NORMAL',
  '食安线上稽核' = 'task:routineTaskManage:fast:FOOD_SAFETY_VIDEO',
  '批量-到店巡检' = 'task:routineTaskManage:batchCreate:normal',
  '批量-视频云巡检' = 'task:routineTaskManage:batchCreate:video',
  '批量-食安线下稽核' = 'task:routineTaskManage:batchCreate:FOOD_SAFETY_NORMAL',
  '批量-食安线上稽核' = 'task:routineTaskManage:batchCreate:FOOD_SAFETY_VIDEO',
  '视频云巡检任务-视频云巡检' = 'task:videoTaskManage:create:video',
  '视频云巡检任务-食安线上稽核' = 'task:videoTaskManage:create:FOOD_SAFETY_VIDEO',
  '视频云巡检任务-批量-视频云巡检' = 'task:videoTaskManage:batch:video',
  '视频云巡检任务-批量-食安线上稽核' = 'task:videoTaskManage:batch:FOOD_SAFETY_VIDEO',

  '日常消杀' = 'task:disinfection:daily',
  '紧急消杀' = 'task:disinfection:emergency',
  '计划配置' = 'task:disinfection:daily:plan',
  '消杀公司配置' = 'task:disinfection:daily:company',
  '排班审核' = 'task:disinfection:daily:examine',
  '结构消杀' = 'task:disinfection:structure',
  // 招聘助手
  '招聘助手' = 'recruit',
  '设备管理' = 'recruit:equipment',
  'BOSS账户管理' = 'recruit:account',
  '岗位打招呼配置' = 'recruit:greet',
  '面试管理' = 'recruit:interview',
  '面试管理-导出' = 'recruit:interview:export',
  '账号运行情况' = 'recruit:running',
  '岗位发布看板' = 'recruit:jobPostingBoard',
  '候选人信息看板' = 'recruit:candidate',
  '候选人管理' = 'recruit:candidate:manage',
  '编辑候选人' = 'recruit:candidate:manage:edit',
  '招聘需求管理' = 'recruit:demand',
  '修改招聘需求' = 'recruit:demand:modify',
  '招聘需求确认招聘' = 'recruit:demand:confirm:recruit',
  '招聘需求配置优先级' = 'recruit:demand:config:priority',
  '招聘需求完成招聘' = 'recruit:demand:finish:recruit',
  '招聘需求中止招聘' = 'recruit:demand:suspend:recruit',
  '招聘需求导出' = 'recruit:demand:suspend:export',
  '招聘需求新增面试' = 'recruit:demand:suspend:add:interview',
  '招聘需求批量导入面试' = 'recruit:demand:suspend:batch:import:interview',
  '招聘需求审核' = 'recruit:demand:audit',
  '招聘审核操作' = 'recruit:demand:audit:operate',

  '常规巡检任务' = 'task:routineNormalTask',
  '常规巡检任务_批次' = 'task:routineNormalTask:batches',
  '创建常规巡检任务' = 'task:routineNormalTask:create',

  '面试跟踪报表' = 'recruit:track',
  '招聘需求驳回' = 'recruit:demand:reject',
  '招聘数据统计看板' = 'recruit:statistic',
  '招聘数据统计看板_需求明细' = 'recruit:statistic:demand',
  '招聘数据统计看板_面试明细' = 'recruit:statistic:interview',

  '困难门店识别' = 'recruit:difficultStore',
  '人工招聘数据管理' = 'recruit:dataManagement',
  '人工招聘数据管理_导入' = 'recruit:dataManagement:import',
  '人工招聘数据管理_新增' = 'recruit:dataManagement:add',
  '人工招聘数据管理_编辑' = 'recruit:dataManagement:edit',
  '人工招聘数据管理_删除' = 'recruit:dataManagement:delete',

  // 候选人资源管理
  '候选人资源管理' = 'recruit:candidate:resource',

  // 统投管理
  '统投管理' = 'invest',
  '统投确认管理' = 'invest:confirm',
  '统投确认管理_计划最低金额配置' = 'invest:confirm:planMinAmount',
  '统投确认管理_投流月度计划管理' = 'invest:confirm:monthPlan',
  '统投确认管理_计划下发' = 'invest:confirm:planIssue',
  '统投确认管理详情' = 'invest:confirm:detail',
  '统投确认管理详情导出' = 'invest:confirm:detail:export',
  '统投确认管理详情导出出价模式信息' = 'invest:confirm:detail:exportBidRatio',
  '统投确认管理详情批量导入' = 'invest:confirm:detail:batchImport',
  '统投确认管理详情导入出价模式信息' = 'invest:confirm:detail:importBidRatio',
  '统投确认管理详情编辑' = 'invest:confirm:detail:edit',
  '统投确认管理详情删除' = 'invest:confirm:detail:delete',
  '统投确认管理详情_操作记录' = 'invest:confirm:detail:operation:record',
  '统投确认管理详情_操作记录_导出' = 'invest:confirm:detail:operation:record:export',
  '统投确认管理日预算表' = 'invest:confirm:daily',
  '美团投流结果查询' = 'invest:mt:result',
  '美团投流结果查询详情' = 'invest:mt:result:detail',
  '美团投流结果查询重试' = 'invest:mt:result:retry',
  '美团投流结果查询移除' = 'invest:mt:result:delete',
  '饿了么投流结果查询' = 'invest:elem:result',
  '饿了么投流结果查询详情' = 'invest:elem:result:detail',
  '饿了么投流结果查询重试' = 'invest:elem:result:retry',
  '饿了么投流结果查询移除' = 'invest:elem:result:delete',
  '管理员配置' = 'invest:administrator',
  '美团投流参数配置' = 'invest:mt:paramsConfig',
  '饿了么投流参数配置' = 'invest:elm:paramsConfig',
  '加投计划管理' = 'invest:addPlan',
  '加投计划管理_新建计划' = 'invest:addPlan:batchCreate',
  '加投计划管理_查看' = 'invest:addPlan:detail',
  '加投计划管理_编辑' = 'invest:addPlan:batchEdit',
  '加投计划管理_通知下发' = 'invest:addPlan:batchStart',
  '加投计划管理_取消' = 'invest:addPlan:batchCancel',
  '加投计划管理_新建计划_批量修改' = 'invest:addPlan:create:batchEdit',
  '加投计划管理_新建计划_导出数据' = 'invest:addPlan:batchExport',
  '加投计划管理_新建计划_批量导入' = 'invest:addPlan:batchImport',
  '加投计划管理_详情_编辑' = 'invest:addPlan:detail:edit',
  '加投计划管理_详情_查看详情' = 'invest:addPlan:detail:view',
  '加投计划管理_详情_导出数据' = 'invest:addPlan:detail:export',
  '加投计划管理_详情_批量调整' = 'invest:addPlan:detail:import',

  // 异常监控
  '异常监控' = 'error:surveillance',
  '异常监控_任务关联AI状态' = 'error:surveillance:task:ai:Status',

  // 策略
  '策略' = 'tactics',
  '策略管理_新建' = 'tactics:add',
  '策略管理_新建食安' = 'tactics:add:food',
  '检查表管理-策略' = 'tactics:checkList',
  '检查项管理-策略' = 'tactics:checkItem',
  '任务中心' = 'tactics:taskCenter',
  '任务模板中心' = 'tactics:templateCenter',
  '报告中心' = 'tactics:reportCenter',
  '整改跟踪-策略' = 'tactics:rectificationStrategy',
  '策略_食安到店辅导报告' = 'tactics:reportCenter:tutor',
  '策略_食安到店辅导报告_稽核确认' = 'tactics:reportCenter:tutor:sure',
  '策略_食安到店辅导报告_点评' = 'tactics:reportCenter:tutor:review',
  '策略_任务转办申请' = 'tactics:taskCenter:transfer',
  '策略_任务转办申请_下载明细' = 'tactics:taskCenter:transfer:download',
  '策略_任务中心' = 'tactics:taskCenter:list',
  '策略_任务中心_作废' = 'tactics:taskCenter:list:cancel',
  '策略_自检报告' = 'tactics:reportCenter:self',
  '策略_常规巡检报告' = 'tactics:reportCenter:routine',
  '策略_常规巡检报告（二方稽核）' = 'tactics:reportCenter:routine:two:square',

  '任务中心_我的任务_视频云巡检' = 'tactics:myTasks:taskCenter:cloud',
  '策略_视频云巡检_线上稽核任务' = 'tactics:taskCenter:cloud:audit',
  '策略_视频云巡检_线上稽核任务_修改执行人' = 'tactics:taskCenter:cloud:audit:modify',
  '策略_视频云巡检_线上专员巡检专项任务' = 'tactics:taskCenter:cloud:patrol',
  '策略_视频云巡检_线上专员巡检专项任务_修改执行人' = 'tactics:taskCenter:cloud:patrol:modify',
  '策略_视频云巡检_线上专员到店人员监管任务' = 'tactics:taskCenter:cloud:arrive',
  '策略_视频云巡检_食安稽核到店辅导点评任务' = 'tactics:taskCenter:cloud:review',
  '条件模板中心' = 'tactics:conditions',
  '转派条件模板' = 'tactics:conditions:transfer',
  '我的任务' = 'myTasks',
  '任务中心_我的任务' = 'taskCenter:myTasks',
  '任务中心_我的任务_点评任务' = 'taskCenter:myTasks:review',
  '任务中心_我的任务_点评任务_点评任务（一点、二点）' = 'taskCenter:myTasks:review:review:menu',
  '任务中心_我的任务_点评任务_点评任务（一点、二点）_待处理' = 'taskCenter:myTasks:review:review:pending',
  '任务中心_我的任务_点评任务_点评任务' = 'taskCenter:myTasks:review:review',
  '任务中心_我的任务_点评任务_修改执行人' = 'taskCenter:myTasks:review:modify',
  '任务中心_我的任务_线上专员到店人员监管任务_修改执行人' = 'taskCenter:myTasks:onlineCheckStaff:modify',
  '任务中心_我的任务_点评任务_食安稽核到店辅导点评任务' = 'taskCenter:myTasks:review:counseling:menu',
  '任务中心_我的任务_点评任务_食安稽核到店辅导点评任务_待处理' = 'taskCenter:myTasks:review:counseling:pending',
  '任务中心_我的任务_食安稽核到店辅导点评任务' = 'taskCenter:myTasks:review:counseling',
  '任务中心_我的任务_食安稽核到店辅导点评任务_修改执行人' = 'taskCenter:myTasks:counseling:modify',
  '任务中心_我的任务_申诉待处理任务' = 'taskCenter:myTasks:appeal:awaitDealTask',
  '任务中心_我的任务_申诉处理任务' = 'taskCenter:myTasks:appeal:dealTask',

  '策略管理' = 'tactics:manage',
  '策略管理_新增食安策略' = 'tactics:manage:food:create',
  '策略管理_新增策略' = 'tactics:manage:create',

  '审核条件模板' = 'tactics:conditions:audit',
  '差异项到店审核' = 'tactics:taskCenter:difference',

  '非食安稽核任务转办申请' = 'tactics:taskCenter:transfer:notFoodsafe',
  '非食安稽核任务转办申请_导出' = 'tactics:taskCenter:transfer:notFoodsafe:export',
  '食安稽核任务转办申请' = 'tactics:taskCenter:transfer:foodsafe',
  '食安稽核任务转办申请_导出' = 'tactics:taskCenter:transfer:foodsafe:export',

  '任务中心_我的任务_食安申诉任务' = 'taskCenter:myTasks:appeal:foodAppeal',
  '任务中心_我的任务_食安申诉任务_待审核' = 'taskCenter:myTasks:appeal:foodAppeal:wait',
  '任务中心_我的任务_食安申诉任务_全部' = 'taskCenter:myTasks:appeal:foodAppeal:all',
  '任务中心_我的任务_食安申诉任务_审核' = 'taskCenter:myTasks:appeal:foodAppeal:audit',
  '任务中心_我的任务_食安申诉任务_查看' = 'taskCenter:myTasks:appeal:foodAppeal:view',

  '任务中心_稽核排班管理' = 'taskCenter:tactics:auditSchedulingManage',
  '任务中心_稽核排班管理_修改批次' = 'taskCenter:tactics:auditSchedulingManage:modify',
  '任务中心_稽核排班管理_修改任务执行信息' = 'taskCenter:tactics:auditSchedulingManage:modify:execute',
  '任务中心_食安异常报备处理' = 'taskCenter:tactics:foodSafeErrorReportApplyDeal',
  '任务中心_食安异常报备处理_审核' = 'taskCenter:tactics:foodSafeErrorReportApplyDeal:audit',

  '事件日志' = 'data:eventLog',

  '预警消息模板' = 'tactics:messageTemplate:warning',
}
