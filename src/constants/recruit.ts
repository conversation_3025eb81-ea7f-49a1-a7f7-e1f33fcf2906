export enum SuspendReasonType {
  RECOMMIT = 'RECOMMIT',
  NO_NEED_1 = 'NO_NEED_1',
  NO_NEED_2 = 'NO_NEED_2',
  NO_NEED_3 = 'NO_NEED_3',
  SHOP_1 = 'SHOP_1',
  SHOP_2 = 'SHOP_2',
  OTHER = 'OTHER',
  SUSPEND_OVER = 'SUSPEND_OVER',
}

export const SuspendReasonTypeCN: Record<SuspendReasonType, string> = {
  [SuspendReasonType.RECOMMIT]: '重新提报需求',
  [SuspendReasonType.NO_NEED_1]: '店员已满',
  [SuspendReasonType.NO_NEED_2]: '门店并未提报需求',
  [SuspendReasonType.NO_NEED_3]: '因其他因素暂停招聘',
  [SuspendReasonType.SHOP_1]: '推送的人员与门店要求不符',
  [SuspendReasonType.SHOP_2]: '无简历推送',
  [SuspendReasonType.OTHER]: '其他',
  [SuspendReasonType.SUSPEND_OVER]: '门店超期未处理约面',
};

export enum SuspendType {
  RECOMMIT = 'RECOMMIT',
  NO_NEED = 'NO_NEED',
  SHOP = 'SHOP',
  OTHER = 'OTHER',
}

export const SuspendTypeCN: Record<SuspendType, string> = {
  [SuspendType.RECOMMIT]: '重新提报需求',
  [SuspendType.NO_NEED]: '无人员缺口',
  [SuspendType.SHOP]: '门店自招',
  [SuspendType.OTHER]: '其他',
};
