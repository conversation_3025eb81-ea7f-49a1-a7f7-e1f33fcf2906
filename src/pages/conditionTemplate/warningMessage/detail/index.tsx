import { useEffect, useMemo } from 'react';
import {
  ProCard,
  ProForm,
  ProFormDependency,
  ProFormDigit,
  ProFormList,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Col, Row, Table } from 'antd';
import { DetailType } from '..';
import Service, { RequestName } from '../service';
import { StrategyPatrolType, StrategyPatrolTypeCN, TaskSubType, TaskSubTypeCN } from '@/constants/strategy';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { computePtTimeStr, parsePtTimeStr } from '@/utils/time';

// 任务子类型配置
const TASK_SUB_TYPE_CONFIG = {
  [TaskSubType.PATROL]: {
    excludeTypes: [
      StrategyPatrolType.VIDEO,
      StrategyPatrolType.FOOD_SAFETY_NORMAL,
      StrategyPatrolType.FOOD_SAFETY_VIDEO,
    ],
    additionalOptions: [],
  },
  [TaskSubType.REVIEW]: {
    excludeTypes: [StrategyPatrolType.VIDEO, StrategyPatrolType.FOOD_SAFETY_VIDEO],
    additionalOptions: [
      {
        label: '自检任务',
        value: 'SELF',
      },
    ],
  },
  [TaskSubType.ISSUE]: {
    excludeTypes: [StrategyPatrolType.VIDEO, StrategyPatrolType.FOOD_SAFETY_VIDEO],
    additionalOptions: [
      {
        label: '自检任务',
        value: 'SELF',
      },
    ],
  },
} as const;

// 生成子任务类型选项的辅助函数
const generateSubTaskOptions = (taskType: TaskSubType) => {
  const config = TASK_SUB_TYPE_CONFIG[taskType];

  if (!config) return [];

  // 基础选项：从 StrategyPatrolTypeCN 中过滤
  const baseOptions = Object.keys(StrategyPatrolTypeCN)
    .filter((key) => !config.excludeTypes.includes(key as StrategyPatrolType))
    .map((key: string) => ({
      label: StrategyPatrolTypeCN[key as StrategyPatrolType],
      value: key,
    }));

  // 合并额外选项
  return [...config.additionalOptions, ...baseOptions];
};

// 子任务类型选择组件
const SubTaskTypeSelect = ({ taskType }: { taskType: TaskSubType }) => {
  const options = generateSubTaskOptions(taskType);

  return (
    <ProFormSelect
      label="子任务类型"
      name="taskSubType"
      rules={[
        {
          required: true,
          message: '请选择子任务类型',
        },
      ]}
      options={options}
      width="md"
    />
  );
};

const timeOpt = [
  {
    label: '8点',
    value: '08:00',
  },
  {
    label: '10点',
    value: '10:00',
  },
  {
    label: '12点',
    value: '12:00',
  },
  {
    label: '14点',
    value: '14:00',
  },
  {
    label: '16点',
    value: '16:00',
  },
  {
    label: '18点',
    value: '18:00',
  },
  {
    label: '20点',
    value: '20:00',
  },
];

export default function DetailIndex() {
  const [form] = ProForm.useForm();
  const [params] = useQuerySearchParams();
  const navigate = useGlobalNavigate();
  const [service, executeRequest] = Service();

  const disabled = useMemo(() => {
    if (!params?.operateType || params?.operateType === DetailType.Readonly) {
      return true;
    }

    return false;
  }, [params?.operateType]);

  useEffect(() => {
    if (params?.id) {
      executeRequest(RequestName.GetMessageWarningTemplateDetail, params?.id).then((res) => {
        const { rules, name, ...rest } = res || {};

        const _rules = rules?.map((r) => {
          const { timeInterval, alertTime } = r || {};
          const _timeInterval = parsePtTimeStr(timeInterval);

          const [days, hours] = _timeInterval || [];

          return {
            timeInterval: {
              days,
              hours,
              unit: days ? 'Days' : 'Hours',
            },
            ...(alertTime && { alertTime }),
          };
        });

        form.setFieldsValue({
          ...rest,
          rules: _rules,
          name: params?.operateType === DetailType.Copy ? `${name} 【副本】` : name,
          id: params?.operateType === DetailType.Edit ? params?.id : undefined,
        });
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ProCard>
      <ProForm
        form={form}
        layout="horizontal"
        labelCol={{ span: 2 }}
        disabled={disabled}
        onFinish={(values) => {
          const { id, rules, ...rest } = values;

          const _rules = rules?.map((r) => {
            const { timeInterval, alertTime } = r || {};

            return {
              ...(alertTime && { alertTime }),
              timeInterval: computePtTimeStr([timeInterval?.days || 0, timeInterval?.hours || 0]),
            };
          });

          const payload = {
            ...rest,
            rules: _rules,
            id,
          };

          executeRequest(
            id ? RequestName.UpdateMessageWarningTemplate : RequestName.CreateMessageWarningTemplate,
            payload,
          ).then(() => {
            navigate(-1);
          });
        }}
      >
        <ProForm.Item name="id" hidden />
        <ProFormText
          name="name"
          label="消息模版名称"
          width="md"
          rules={[{ required: true, message: '请输入消息模版名称' }]}
        />
        <ProFormSelect
          name="taskType"
          label="关联任务类型"
          width="md"
          rules={[{ required: true, message: '请选择关联任务类型' }]}
          valueEnum={TaskSubTypeCN}
        />
        <ProFormDependency name={['taskType']}>
          {({ taskType }) => {
            // 只有当任务类型是巡检、点评或整改时才显示子任务类型选择器
            if ([TaskSubType.PATROL, TaskSubType.REVIEW, TaskSubType.ISSUE].includes(taskType as TaskSubType)) {
              return <SubTaskTypeSelect taskType={taskType as TaskSubType} />;
            }

            return null;
          }}
        </ProFormDependency>
        <ProFormText
          name="messageTitle"
          label="消息名称"
          width="md"
          rules={[{ required: true, message: '请输入消息名称' }]}
        />

        <Row>
          <Col span={8}>
            <ProFormTextArea
              width="md"
              label="消息内容"
              name="messageContent"
              placeholder="请输入消息内容"
              rules={[
                {
                  required: true,
                  message: '请输入消息内容',
                },
              ]}
              fieldProps={{
                maxLength: 400,
                showCount: true,
                autoSize: {
                  minRows: 6,
                },
              }}
              labelCol={{ span: 6 }}
            />
          </Col>
          <Col>
            <p>
              <span>填写说明：</span>
              请将以下所列字段对应的变量码值复制到左侧输入框中，请注意复制完整内容。
            </p>
            <Table
              className="message-setting-variables"
              columns={[
                {
                  title: '字段名称',
                  dataIndex: 'name',
                },
                {
                  title: '变量码值',
                  dataIndex: 'symbol',
                },
              ]}
              dataSource={[
                {
                  name: '门店名称',
                  // eslint-disable-next-line no-template-curly-in-string
                  symbol: '${shopName}',
                },
                {
                  name: '任务名称',
                  // eslint-disable-next-line no-template-curly-in-string
                  symbol: '${taskName}',
                },
                {
                  name: '任务类型',
                  // eslint-disable-next-line no-template-curly-in-string
                  symbol: '${taskType}',
                },
              ]}
            />
          </Col>
        </Row>
        <ProFormList
          name="rules"
          alwaysShowItemLabel
          initialValue={[{}]} // 添加默认一行数据
          creatorButtonProps={{
            creatorButtonText: '新增预警节点',
          }}
          min={1}
          max={10}
        >
          {(f, index, action) => {
            return (
              <>
                <ProForm.Item
                  label={`任务${index === 0 ? '初' : ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'][index]}次预警提前时间`}
                  required
                >
                  <ProFormDependency name={['timeInterval']}>
                    {({ timeInterval }) => {
                      const maxDays = timeInterval?.unit === 'Days' ? 31 : 23;

                      return (
                        <ProFormDigit
                          name={timeInterval?.unit === 'Days' ? ['timeInterval', 'days'] : ['timeInterval', 'hours']}
                          min={1}
                          max={maxDays}
                          rules={[
                            { required: true, message: '请输入正整数' },
                            {
                              validator: (_, value) => {
                                if (value > maxDays) {
                                  return Promise.reject(`不能大于${maxDays}`);
                                }

                                return Promise.resolve();
                              },
                            },
                          ]}
                          noStyle
                          width="xs"
                          preserve={false}
                        />
                      );
                    }}
                  </ProFormDependency>

                  <ProFormSelect
                    name={['timeInterval', 'unit']}
                    options={[
                      {
                        label: '天',
                        value: 'Days',
                      },
                      {
                        label: '小时',
                        value: 'Hours',
                      },
                    ]}
                    initialValue={'Days'}
                    noStyle
                    width="xs"
                    rules={[{ required: true, message: '请选择' }]}
                    fieldProps={{
                      onChange: (value) => {
                        // 当单位切换时，清理相关字段
                        const currentValues = form.getFieldValue(['rules', index]);
                        const newTimeInterval = { unit: value };

                        // 清理旧的时间间隔字段和提醒时间
                        form.setFieldValue(['rules', index], {
                          ...currentValues,
                          timeInterval: newTimeInterval,
                          alertTime: undefined, // 清理提醒时间
                        });
                      },
                    }}
                  />
                </ProForm.Item>
                <ProFormDependency name={['timeInterval']}>
                  {({ timeInterval }) => {
                    if (timeInterval?.unit === 'Days') {
                      return (
                        <ProFormSelect
                          options={timeOpt}
                          label="提醒时间点"
                          name={['alertTime']}
                          rules={[{ required: true, message: '请选择提醒时间点' }]}
                          preserve={false}
                        />
                      );
                    }

                    return null;
                  }}
                </ProFormDependency>
              </>
            );
          }}
        </ProFormList>
      </ProForm>
    </ProCard>
  );
}
