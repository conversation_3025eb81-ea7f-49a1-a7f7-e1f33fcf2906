import { useEffect, useRef, useState } from 'react';
import { ActionType, ProCard, ProForm, ProTable } from '@ant-design/pro-components';
import { Button, Space } from 'antd';
import { isEmpty } from 'lodash';
import { TemplateStatus, TemplateStatusButtonNameMap, TemplateStatusCN } from './constant';
import Service, { RequestName } from './service';
import { getColumns, getCommonConfig, getPersonColumn } from '@/components/pro-table-config';
import { StrategyTemplateTypeCN, TaskSubTypeCN } from '@/constants/strategy';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import useUserListCaching from '@/hooks/use-user-list-caching';
import { RouteKey } from '@/router/config';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { parserParams } from '@/utils/convert';
import { PaginationParams } from '@/utils/pagination';
import { renderPage } from '@/utils/render';

export enum DetailType {
  Add = 'Add', // 新增
  Readonly = 'Readonly', // 只读
  Edit = 'Edit', // 编辑
  Copy = 'Copy', // 复制
}

const WarningMessageIndex = renderPage(() => {
  const [form] = ProForm.useForm();
  const actionRef = useRef<ActionType>();
  const [service, executeRequest] = Service();
  const navigate = useGlobalNavigate();

  const { userListCachingRef } = useUserListCaching({ options: service?.reviewerOptions });
  const [urlParams, setUrlParams] = useQuerySearchParams();
  const _wMessage = parserParams(urlParams?.wMessage || {});
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({ pageNo: 1, pageSize: 10 });

  const columns = getColumns({
    searchColumns: [
      {
        title: '预警消息模版名称',
        dataIndex: 'name',
        colSize: 1.5,
      },
      {
        title: '状态',
        dataIndex: 'status',
        valueType: 'select',
        valueEnum: {
          [TemplateStatus.生效中]: TemplateStatusCN[TemplateStatus.生效中],
          [TemplateStatus.未生效]: TemplateStatusCN[TemplateStatus.未生效],
        },
      },

      getPersonColumn(
        {
          options: userListCachingRef?.current || service?.reviewerOptions,
          onFocus: () => {
            if (!userListCachingRef?.current) {
              executeRequest(RequestName.GetUserInfoList);
            }
          },
          loading: service?.reviewerLoading,
        },
        {
          dataIndex: 'createBy',
          title: '创建人',
          colSize: 1.5,
        },
      ),
      { title: '任务类型', dataIndex: 'taskType', valueType: 'select', valueEnum: TaskSubTypeCN },
    ],
    tableColumns: [
      {
        title: '预警消息模版名称',
        dataIndex: 'name',
        render: (_, record) => (
          <a
            onClick={() => {
              navigate(RouteKey.WMessageDetail, {
                searchParams: {
                  operateType: DetailType.Readonly,
                  id: record?.id,
                },
              });
            }}
          >
            {record?.name}
          </a>
        ),
      },
      {
        title: '任务类型',
        dataIndex: 'taskType',
        render: (_, record) => `${StrategyTemplateTypeCN[record?.taskType]}任务`,
      },
      {
        title: '创建人',
        dataIndex: 'createByName',
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        valueType: 'dateTime',
      },
      {
        title: '状态',
        dataIndex: 'status',
        valueEnum: {
          [TemplateStatus.生效中]: {
            text: TemplateStatusCN[TemplateStatus.生效中],
            status: 'success',
          },
          [TemplateStatus.未生效]: {
            text: TemplateStatusCN[TemplateStatus.未生效],
            status: 'error',
          },
        },
      },
      {
        title: '操作',
        dataIndex: 'index',
        render: (_, record) => {
          return (
            <Space>
              <a
                onClick={() => {
                  executeRequest(
                    record?.status === TemplateStatus.生效中
                      ? RequestName.DisableMessageWarningTemplate
                      : RequestName.EnableMessageWarningTemplate,
                    record?.id,
                  ).then(() => {
                    actionRef?.current?.reload();
                  });
                }}
              >
                {TemplateStatusButtonNameMap[record?.status]}
              </a>
              <a
                onClick={() => {
                  navigate(RouteKey.WMessageDetail, {
                    searchParams: { operateType: DetailType.Copy, id: record?.id },
                  });
                }}
              >
                复制
              </a>
              <a
                onClick={() => {
                  navigate(RouteKey.WMessageDetail, {
                    searchParams: { operateType: DetailType.Edit, id: record?.id },
                  });
                }}
              >
                编辑
              </a>
            </Space>
          );
        },
      },
    ],
  });

  useEffect(() => {
    if (!isEmpty(_wMessage)) {
      if (_wMessage?.createBy) {
        executeRequest(RequestName.GetUserInfoList);
      }

      form.setFieldsValue({
        ..._wMessage,
      });

      setPaginationParams({
        pageNo: _wMessage?.pageNo || 1,
        pageSize: _wMessage?.pageSize || 10,
      });
    }

    form.submit();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ProTable
      rowKey="id"
      {...getCommonConfig({
        search: {
          form,
        },
        manualRequest: true,
      })}
      actionRef={actionRef}
      options={false}
      columns={columns}
      manualRequest={true}
      request={async (values) => {
        const { current, pageSize, ...val } = values;

        const payload = {
          pageNo: current || _wMessage?.current || 1,
          pageSize: pageSize || _wMessage?.pageSize || 10,
          ...val,
        };

        setUrlParams({
          ...urlParams,
          wMessage: {
            ...val,
            pageNo: current || _wMessage?.current || 1,
            pageSize: pageSize || _wMessage?.pageSize || 10,
          },
        });

        const res = await executeRequest(RequestName.GetMessageWarningTemplateList, payload);

        return {
          data: res?.data || [],
          success: true,
          total: res?.total,
        };
      }}
      pagination={{
        showSizeChanger: true,
        current: paginationParams.pageNo,
        pageSize: paginationParams.pageSize,
        onChange: (pageNo, pageSize) => {
          setPaginationParams({ pageNo, pageSize });
        },
      }}
      tableExtraRender={() => {
        return (
          <ProCard>
            <Button
              type="primary"
              onClick={() => {
                navigate(RouteKey.WMessageDetail, {
                  searchParams: { operateType: DetailType.Add },
                });
              }}
            >
              创建预警消息模板
            </Button>
          </ProCard>
        );
      }}
      tableRender={(_p, _d, { table }) => {
        return <ProCard>{table}</ProCard>;
      }}
    />
  );
});

export default WarningMessageIndex;
