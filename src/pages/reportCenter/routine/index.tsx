/* eslint-disable max-lines-per-function */
import { FC, ReactNode, useEffect, useRef, useState } from 'react';
import { ActionType, ProCard, ProForm, ProTable } from '@ant-design/pro-components';
import { Button } from 'antd';
import dayjs from 'dayjs';
import Service, { RequestName } from './service';
import { getTableColumns } from './table-config';
import { DateQueryType, DateType } from '@/components/date-query-select';
import { DownloadButton } from '@/components/DownloadButton';
import {
  getChecklistColumn,
  getColumns,
  getCommonConfig,
  getDiagnosisTypeColumn,
  getInspectionTypeColumn,
  getPersonColumn,
  getPersonWithStaffCodeColumn,
  getReportPassedColumn,
  getReportStatusColumn,
  getStoreColumn,
  getStoreTypeColumn,
  onReset,
} from '@/components/pro-table-config';
import getDateQueryColumn from '@/components/pro-table-config/date-query';
import getHandleRoleTypeColumn from '@/components/pro-table-config/handle-role-type';
import { getTaskTemplateColumn } from '@/components/pro-table-config/task-template';
import TaskDrawer, { ShowTaskType } from '@/components/task-drawer';
import { ChecklistStrategyType } from '@/constants/checklist-strategy';
import { NotFilledItemHandleType, StrategyPatrolType, StrategyPatrolTypeCN } from '@/constants/strategy';
import { DiagnosisTacticsCN } from '@/constants/task';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import useUserListCaching from '@/hooks/use-user-list-caching';
import { RouteKey } from '@/router/config';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { formatDateToUTC } from '@/utils/date';
import { disabled30Date } from '@/utils/disabled';
import { formatQueryDate } from '@/utils/format';
import { renderPage } from '@/utils/render';
import { formatUrlObjToObject } from '@/utils/syncToUrl';

const Routine: FC = renderPage(() => {
  const [form] = ProForm.useForm();
  const commonConfig = getCommonConfig({
    search: {
      form,
    },
  });
  const actionRef = useRef<ActionType>();
  const [params, setParams] = useQuerySearchParams();
  const [service, executeRequest] = Service();
  const [drawerProps, setDrawerProps] = useState<{
    open: boolean;
    taskId?: string | number;
    showType?: ShowTaskType;
    title?: string;
    needReviewSummary?: boolean;
    needStatistics?: boolean;
    needExam?: boolean;
    hasReview?: boolean;
    reviewSumUp?: string; // 点评总结
    notFilledItemHandleType?: NotFilledItemHandleType;
    footer?: ReactNode;
  }>({
    open: false,
  });
  const navigate = useGlobalNavigate();

  const { userListCachingRef } = useUserListCaching({ options: service?.personOptions });

  const columns = getColumns({
    searchColumns: [
      getStoreColumn({
        organizationOptions: service?.organizationOptions,
        storeOptions: service?.storeOptions,
        onStoreFocus: (groupId: any) => {
          executeRequest(RequestName.GetStoreList, {
            fightGroupId: groupId,
            groupType: 2,
            privilegeCode: 1,
          });
        },
      }),
      getStoreTypeColumn({}, { dataIndex: 'shopType' }),
      getDateQueryColumn({
        disabledDate: disabled30Date,
        limit: 30,
        typeOptions: [
          {
            value: DateQueryType.TaskDeliveryDate,
            label: '基础任务下发日期',
          },
          {
            value: DateQueryType.ReportSubmissionTime,
            label: '报告提交日期',
          },
          {
            value: DateQueryType.BeginpatrolDate,
            label: '开始巡检时间',
          },
        ],
      }),
      // getRoleColumn({ options: service?.roleOptions, multiple: true }, { dataIndex: 'roleIdList' }),
      getPersonColumn(
        {
          options: userListCachingRef?.current || service?.personOptions,
          mode: 'multiple',
          onFocus: () => {
            if (!userListCachingRef?.current) {
              executeRequest(RequestName.GetUserInfoList);
            }
          },
          loading: service?.personLoading,
        },
        { dataIndex: 'taskUserIds', title: '巡检人' },
      ),
      getChecklistColumn({
        tagOptions: service?.tagOptions,
        checklistOptions: service?.checklistOptions,
        onTagChange: (value) => {
          requestChecklist(value);
        },
      }),
      getInspectionTypeColumn(
        {
          options: Object.keys(StrategyPatrolTypeCN)
            .filter((key) => key !== StrategyPatrolType.FOOD_SAFETY_ARRIVE_SHOP)
            .map((key) => ({
              label: StrategyPatrolTypeCN[key as keyof typeof StrategyPatrolTypeCN],
              value: key as keyof typeof StrategyPatrolTypeCN,
            })),
        },
        { dataIndex: 'taskSubType' },
      ),
      getDiagnosisTypeColumn({ valueEnum: DiagnosisTacticsCN }),
      getReportStatusColumn(
        {
          valueEnum: {
            WAITING_CONFIRM: '待确认',
            CONFIRMED: '已确认',
            CANCELED: '已作废',
          },
        },
        { dataIndex: 'reportStatus', title: '报告状态' },
      ),
      getReportPassedColumn(),
      // getReviewStatusColumn(),
      // getReviewPassedColumn(),
      getHandleRoleTypeColumn(),
      getTaskTemplateColumn({ options: service?.templateOptions }, { dataIndex: 'templateId', colSize: 1.5 }),
      // getPersonColumn(
      //   {
      //     options: userListCachingRef?.current || service?.personOptions,
      //     onFocus: () => {
      //       if (!userListCachingRef?.current) {
      //         executeRequest(RequestName.GetUserInfoList);
      //       }
      //     },
      //     loading: service?.personLoading,
      //   },
      //   { dataIndex: 'identifiedUser', title: '被鉴定人' },
      // ),
      getPersonWithStaffCodeColumn(),
    ],
    tableColumns: getTableColumns({
      onReview: (record) => {
        setDrawerProps({
          open: true,
          taskId: record?.baseTaskId,
          showType: ShowTaskType.Review,
          needReviewSummary: true,
          title: '任务点评',
          hasReview: record?.hasNeedReview,
        });
      },
      onView: async (record) => {
        setDrawerProps({
          open: true,
          taskId: record?.baseTaskId,
          showType: ShowTaskType.Detail,
          needReviewSummary: true,
          needStatistics: true,
          title: '任务详情',
        });
      },
      onIssueCount: (record) => {
        navigate(RouteKey.RCRTrack, {
          searchParams: { baseTaskId: record?.baseTaskId },
        });
      },
    }),
  });

  const requestChecklist = (labelIds?: number[]) => {
    return executeRequest(RequestName.GetChecklistSimplelist, {
      labelIds,
      worksheetTypes: [ChecklistStrategyType.PATROL],
    });
  };

  useEffect(() => {
    executeRequest(RequestName.GetGroupTreeList);
    requestChecklist();
    executeRequest(RequestName.GetTags);

    if (params?.taskUserIds?.length) {
      executeRequest(RequestName.GetUserInfoList);
    }

    executeRequest(RequestName.GetRoleCascaderData, 1);
    executeRequest(RequestName.QueryBaseTemplateList, {
      type: ChecklistStrategyType.PATROL,
    });

    if (params?.store) {
      const { groupId, shopIds } = JSON.parse(params?.store);

      if (groupId || shopIds) {
        executeRequest(RequestName.GetStoreList, {
          fightGroupId: groupId,
          groupType: 2,
          privilegeCode: 1,
        });
      }
    }
  }, []);

  const formatParams = (params) => {
    const { store, shopType, current, pageSize, sheet, status, passed, dateQuery, templateId, ...rest } = params;

    const { startDate, endDate } = formatQueryDate(dateQuery);

    const dateQueryMap = {
      [DateQueryType.TaskDeliveryDate]: {
        startDate: formatDateToUTC(startDate),
        endDate: formatDateToUTC(endDate),
      },
      [DateQueryType.ReportSubmissionTime]: {
        submitStartTime: formatDateToUTC(startDate),
        submitEndTime: formatDateToUTC(endDate),
      },
      [DateQueryType.BeginpatrolDate]: {
        taskStartTime: formatDateToUTC(startDate),
        taskEndTime: formatDateToUTC(endDate),
      },
    };

    return {
      ...store,
      ...rest,
      shopType,
      pageSize: pageSize ?? 20,
      pageNo: current ?? 1,
      worksheetId: sheet?.checklist,
      status,
      passed,
      // startDate: formatDateToUTC(startDate),
      // endDate: formatDateToUTC(endDate),
      taskType: 'PATROL',
      // isSubmitTime: dateQuery?.type === DateQueryType.ReportSubmissionTime,
      templateId,
      ...(dateQueryMap[dateQuery?.type] || {}),
    };
  };

  return (
    <>
      <ProTable
        {...commonConfig}
        columns={columns}
        actionRef={actionRef}
        tableExtraRender={() => (
          <div className="flex justify-end bg-white py-3 px-5 rounded">
            <DownloadButton
              downloadReq={() => {
                const values = form.getFieldsValue();

                return executeRequest(RequestName.exportReport, formatParams(values));
              }}
            />
          </div>
        )}
        form={{
          syncToUrl: (values, type) => {
            // 主动排查hikUserId
            const { hikUserId, ...rest } = values || {};

            if (type === 'get') {
              const formatValues = formatUrlObjToObject(values);
              const { passed, dateQuery, taskUserIds } = formatValues;

              return {
                ...formatValues,
                taskUserIds: taskUserIds?.map((id) => +id),
                passed: passed?.toString(),
                dateQuery: dateQuery
                  ? {
                      ...dateQuery,
                      date: dateQuery?.date
                        ? Array.isArray(dateQuery?.date)
                          ? [dayjs(dateQuery?.date?.[0]), dayjs(dateQuery?.date?.[1])]
                          : dayjs(dateQuery?.date)
                        : undefined,
                    }
                  : {
                      date: dayjs().add(-1, 'days'),
                      type: DateQueryType.TaskDeliveryDate,
                      dateType: DateType.Yesterday,
                    },
              };
            }

            return {
              ...rest,
            };
          },
        }}
        onReset={() => {
          onReset(form, {
            dateQuery: {
              date: dayjs().add(-1, 'days'),
              type: DateQueryType.TaskDeliveryDate,
              dateType: DateType.Yesterday,
            },
          });
          requestChecklist();
        }}
        scroll={{ x: 3200 }}
        tableRender={(_p, _d, { table }) => {
          return <ProCard>{table}</ProCard>;
        }}
        request={async (values) => {
          const res = await executeRequest(RequestName.GetReportCenterList, formatParams(values));

          return {
            data: res?.data,
            total: res?.total,
            success: true,
          };
        }}
      />
      <TaskDrawer
        destroyOnClose={true}
        width={650}
        title={drawerProps?.title}
        open={drawerProps?.open}
        onClose={() => setDrawerProps({ open: false })}
        showType={drawerProps?.showType}
        taskId={drawerProps?.taskId}
        needReviewSummary={drawerProps?.needReviewSummary}
        needStatistics={drawerProps?.needStatistics}
        hasReview={drawerProps?.hasReview}
        reviewSumUp={drawerProps?.reviewSumUp}
        notFilledItemHandleType={drawerProps?.notFilledItemHandleType}
        footer={drawerProps?.footer}
        showOperationRecord
        onPreview={({ reviewSumUp, notFilledItemHandleType }) => {
          setDrawerProps({
            ...drawerProps,
            showType: ShowTaskType.Detail,
            needStatistics: true,
            needReviewSummary: true,
            reviewSumUp,
            notFilledItemHandleType,
            footer: (
              <Button
                type="primary"
                className="w-full"
                onClick={async () => {
                  if (drawerProps?.reviewSumUp) {
                    await executeRequest(RequestName.SubmitReviewSummary, {
                      taskId: drawerProps?.taskId,
                      summary: drawerProps?.reviewSumUp,
                    });
                  }

                  await executeRequest(RequestName.SubmitReviewTask, {
                    baseTaskId: drawerProps?.taskId,
                    notFilledItemHandleType: notFilledItemHandleType || NotFilledItemHandleType.SET_FULL_SCORE,
                  });
                  setDrawerProps({
                    open: false,
                  });
                  actionRef.current.reload();
                }}
              >
                提交点评
              </Button>
            ),
          });
        }}
        saveReviewItem={(dto) => {
          return executeRequest(RequestName.SaveReviewItem, dto);
        }}
      />
    </>
  );
});

export default Routine;
