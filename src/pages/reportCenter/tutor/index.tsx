import { ReactNode, useEffect, useRef, useState } from 'react';
import { ActionType, ProCard, ProForm, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import Service, { RequestName } from './service';
import { getTableColumns } from './table-config';
import { DateQueryType, DateType } from '@/components/date-query-select';
import { DownloadButton } from '@/components/DownloadButton';
import {
  getChecklistColumn,
  getColumns,
  getCommonConfig,
  getPersonColumn,
  getReviewPassedColumn,
  getReviewStatusColumn,
  getStoreColumn,
  getStoreTypeColumn,
  onReset,
} from '@/components/pro-table-config';
import getDateQueryColumn from '@/components/pro-table-config/date-query';
import TaskDrawer, { ShowTaskType } from '@/components/task-drawer';
import { ChecklistStrategyType } from '@/constants/checklist-strategy';
import { NotFilledItemHandleType } from '@/constants/strategy';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import useUserListCaching from '@/hooks/use-user-list-caching';
import useTutorSureModal from '@/pages/report/tutor/hooks/use-tutor-sure-modal';
import { RouteKey } from '@/router/config';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { formatDateToUTC } from '@/utils/date';
import { disabled30Date } from '@/utils/disabled';
import { formatQueryDate } from '@/utils/format';
import { formatParamToArray } from '@/utils/param';
import { formatUrlObjToObject } from '@/utils/syncToUrl';

// eslint-disable-next-line max-lines-per-function
const Tutor = () => {
  const [form] = ProForm.useForm();
  const [params]: any = useQuerySearchParams();
  const [service, executeRequest] = Service();
  const commonConfig = getCommonConfig({
    search: {
      form,
    },
  });
  const [drawerProps, setDrawerProps] = useState<{
    open: boolean;
    taskId?: string | number;
    showType?: ShowTaskType;
    title?: string;
    needReviewSummary?: boolean;
    needStatistics?: boolean;
    needExam?: boolean;
    hasReview?: boolean;
    reviewSumUp?: string; // 点评总结
    notFilledItemHandleType?: NotFilledItemHandleType;
    footer?: ReactNode;
  }>({
    open: false,
  });
  const navigate = useGlobalNavigate();

  const { showModal } = useTutorSureModal();

  const actionRef = useRef<ActionType>();

  const requestChecklist = (labelIds?: number[]) => {
    return executeRequest(RequestName.GetChecklistSimplelist, {
      labelIds,
      worksheetTypes: [ChecklistStrategyType.PATROL],
    });
  };

  useEffect(() => {
    executeRequest(RequestName.GetGroupTreeList);
    executeRequest(RequestName.GetRoleCascaderData, 1);

    executeRequest(RequestName.GetTags);
    requestChecklist();

    if (params?.taskUserIds?.length) {
      executeRequest(RequestName.GetUserInfoList);
    }

    if (params?.store) {
      const { groupId } = JSON.parse(params?.store);

      executeRequest(RequestName.GetStoreList, groupId);
    }
  }, []);

  const { userListCachingRef } = useUserListCaching({ options: service?.reviewerOptions });

  const columns = getColumns({
    searchColumns: [
      getStoreColumn({
        organizationOptions: service?.organizationOptions,
        storeOptions: service?.storeOptions,
        storeLoading: service?.storeLoading,
        onStoreFocus: (groupId: any) => {
          executeRequest(RequestName.GetStoreList, groupId);
        },
      }),
      getStoreTypeColumn(null, { dataIndex: 'shopType' }),
      getDateQueryColumn({
        disabledDate: disabled30Date,
        limit: 30,
        typeOptions: [
          {
            value: DateQueryType.TaskDeliveryDate,
            label: '基础任务下发日期',
          },
          {
            value: DateQueryType.ReportSubmissionTime,
            label: '报告提交日期',
          },
          {
            value: DateQueryType.BeginpatrolDate,
            label: '开始巡检时间',
          },
        ],
      }),
      // getRoleColumn({ options: service?.roleOptions, multiple: true }, { dataIndex: 'roleIdList' }),
      getPersonColumn(
        {
          options: userListCachingRef?.current || service?.reviewerOptions,
          mode: 'multiple',
          onFocus: () => {
            if (!userListCachingRef?.current) {
              executeRequest(RequestName.GetUserInfoList);
            }
          },
          loading: service?.reviewerLoading,
        },
        { dataIndex: 'taskUserIds', title: '巡检人' },
      ),
      getChecklistColumn(
        {
          tagOptions: service?.tagOptions,
          checklistOptions: service?.checklistOptions,
          onTagChange: (value) => {
            requestChecklist(value);
          },
        },
        { dataIndex: 'worksheetId' },
      ),
      getReviewStatusColumn(),
      getReviewPassedColumn(null, { dataIndex: 'reviewPassed' }),
    ],
    tableColumns: [
      {
        title: '任务名称',
        dataIndex: 'taskName',
        fixed: 'left',
        width: 200,
        render: (_, record) => (
          <a
            onClick={() => {
              setDrawerProps({
                open: true,
                taskId: record?.baseTaskId,
                showType: ShowTaskType.Detail,
                needReviewSummary: true,
                needStatistics: true,
                title: '任务详情',
              });
            }}
          >
            {record?.taskName}
          </a>
        ),
      },
      ...getTableColumns({
        onIssueCount: (record) => {
          navigate(RouteKey.RCRTrack, {
            searchParams: { baseTaskId: record?.baseTaskId },
          });
        },
      }),
      // {
      //   title: '操作',
      //   dataIndex: 'operate',
      //   fixed: 'right',
      //   width: 140,
      //   render: (_, record) => {
      //     const {
      //       taskName,
      //       onlineAuditConfirm,
      //       hasNeedReview,
      //       shopId,
      //       shopName,
      //       baseTaskId,
      //       reviewStatus,
      //       reportStatus,
      //     } = record;

      //     return (
      //       <Space>
      //         <Permission permission={Auth.策略_食安到店辅导报告_稽核确认}>
      //           {reportStatus === 'CONFIRMED' && !onlineAuditConfirm && (
      //             <a
      //               onClick={() => {
      //                 showModal({
      //                   type: TutorSureModalType.Edit,
      //                   initialValues: { taskName, shopId, shopName, taskId: baseTaskId, hasNeedReview, reviewStatus },
      //                   onOk: (data) => {
      //                     return executeRequest(RequestName.ConfirmStrategyTutorAudit, data).then(() => {
      //                       actionRef?.current?.reload();
      //                     });
      //                   },
      //                 });
      //               }}
      //             >
      //               稽核确认
      //             </a>
      //           )}
      //         </Permission>
      //         {onlineAuditConfirm && (
      //           <a
      //             onClick={() => {
      //               executeRequest(RequestName.GetStrategyConfirmTutorInfo, baseTaskId).then((res) => {
      //                 showModal({
      //                   type: TutorSureModalType.View,
      //                   initialValues: {
      //                     taskName,
      //                     shopId,
      //                     shopName,
      //                     ...res,
      //                     examSituationConfirm: res?.examSituationConfirm?.toString(),
      //                   },
      //                 });
      //               });
      //             }}
      //           >
      //             稽核确认详情
      //           </a>
      //         )}
      //         <Permission permission={Auth.策略_食安到店辅导报告_点评}>
      //           {(isNil(onlineAuditConfirm) || onlineAuditConfirm) && hasNeedReview && (
      //             <a
      //               onClick={() => {
      //                 showReport(record, false);
      //               }}
      //             >
      //               点评
      //             </a>
      //           )}
      //         </Permission>
      //       </Space>
      //     );
      //   },
      // },
    ],
  });

  const formatParams = (params) => {
    const {
      current,
      pageSize,
      dateQuery,
      store,
      taskUserIds,
      taskStatus,
      taskType,
      shopType,
      roleIdList,
      worksheetId,
      reviewPassed,
      reviewStatus,
    } = params;

    const { startDate, endDate } = formatQueryDate(dateQuery);

    const dateQueryMap = {
      [DateQueryType.TaskDeliveryDate]: {
        startDate: formatDateToUTC(startDate),
        endDate: formatDateToUTC(endDate),
      },
      [DateQueryType.ReportSubmissionTime]: {
        submitStartTime: formatDateToUTC(startDate),
        submitEndTime: formatDateToUTC(endDate),
      },
      [DateQueryType.BeginpatrolDate]: {
        taskStartTime: formatDateToUTC(startDate),
        taskEndTime: formatDateToUTC(endDate),
      },
    };

    return {
      ...store,
      pageNo: current,
      pageSize,

      // startDate: formatDateToUTC(startDate),
      // endDate: formatDateToUTC(endDate),
      // isSubmitTime: dateQuery?.type === DateQueryType.ReportSubmissionTime,
      taskStatus,
      taskUserIds,
      taskType,
      shopType,
      roleIdList,
      worksheetId: worksheetId?.checklist,
      reviewPassed,
      reviewStatus,
      ...(dateQueryMap[dateQuery?.type] || {}),
    };
  };

  return (
    <>
      <ProTable
        {...commonConfig}
        columns={columns}
        actionRef={actionRef}
        scroll={{ x: 1800 }}
        tableRender={(_p, _d, { table }) => {
          return (
            <ProCard
              extra={
                <DownloadButton
                  downloadReq={() => {
                    const values = form.getFieldsValue();

                    // 正常导出是不需要pageSize和pageNo的，后端校验需要，前端直接写死
                    return executeRequest(RequestName.ExportStrategyTutorReportList, {
                      ...formatParams(values),
                      pageSize: 20,
                      pageNo: 1,
                    });
                  }}
                />
              }
            >
              {table}
            </ProCard>
          );
        }}
        request={async (params) => {
          const res = await executeRequest(RequestName.GetStrategyTutorReportList, formatParams(params));

          return { data: res?.data, total: res?.total, success: true };
        }}
        form={{
          syncToUrl: (values: any, type: any) => {
            if (type === 'get') {
              const { store, roleIdList, worksheetId, dateQuery, taskUserIds } = formatUrlObjToObject(values);

              return {
                ...values,
                roleIdList: formatParamToArray(roleIdList),
                worksheetId,
                taskUserIds: taskUserIds?.map((id) => +id),
                dateQuery: dateQuery
                  ? {
                      ...dateQuery,
                      date: dateQuery?.date
                        ? Array.isArray(dateQuery?.date)
                          ? [dayjs(dateQuery?.date?.[0]), dayjs(dateQuery?.date?.[1])]
                          : dayjs(dateQuery?.date)
                        : undefined,
                    }
                  : {
                      date: dayjs().add(-1, 'days'),
                      type: DateQueryType.TaskDeliveryDate,
                      dateType: DateType.Yesterday,
                    },
                store: {
                  groupId: store?.groupId,
                  shopIds: store?.shopIds,
                },
              };
            }

            return {
              ...values,
            };
          },
        }}
        onReset={() =>
          onReset(form, {
            dateQuery: {
              date: dayjs().add(-1, 'days'),
              type: DateQueryType.TaskDeliveryDate,
              dateType: DateType.Yesterday,
            },
          })
        }
      />
      <TaskDrawer
        destroyOnClose={true}
        width={650}
        title={drawerProps?.title}
        open={drawerProps?.open}
        onClose={() => setDrawerProps({ open: false })}
        showType={drawerProps?.showType}
        taskId={drawerProps?.taskId}
        needReviewSummary={drawerProps?.needReviewSummary}
        needStatistics={drawerProps?.needStatistics}
        needExam={true}
        showOperationRecord
      />
    </>
  );
};

export default Tutor;
