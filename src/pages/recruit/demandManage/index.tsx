/* eslint-disable max-lines-per-function */
/* eslint-disable react-refresh/only-export-components */
import { Key, useEffect, useMemo, useRef, useState } from 'react';
import { ActionType, ProCard, ProTable } from '@ant-design/pro-components';
import { isInIcestark } from '@ice/stark-app';
import { Button, Form, message, Modal, ModalFuncProps, Tabs } from 'antd';
import dayjs from 'dayjs';
import BatchImportInterview from './components/BatchImportInterview';
import Service, { RequestName } from './service';
import { getTableColumns, searchColumns } from './table-config';
import useGreetModal, { GreetModalType } from '../greet/hooks/use-greet-modal';
import { Permission } from '@/components/Permission';
import { getColumns, getCommonConfig } from '@/components/pro-table-config';
import { getOrganizationTreeColumn } from '@/components/pro-table-config/organization-tree-select';
import { Auth } from '@/constants/auth';
import { SuspendReasonType, SuspendReasonTypeCN, SuspendType, SuspendTypeCN } from '@/constants/recruit';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { RouteKey } from '@/router/config';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { formatDateToUTC } from '@/utils/date';
import formatFormStr from '@/utils/formatFormStr';
import { windowOpen } from '@/utils/jump';
import { renderPage } from '@/utils/render';

function showConfirmModal({ title, content, ...restModalProps }: ModalFuncProps) {
  Modal.confirm({
    title,
    content: <div className="text-center py-4">{content}</div>,
    width: 300,
    icon: null,
    ...restModalProps,
  });
}

const allButtons = [
  {
    label: '确认招聘',
    actionType: 'CONFIRM',
    permission: Auth.招聘需求确认招聘,
  },
  {
    label: '配置优先级',
    actionType: GreetModalType.ConfigPriority,
    permission: Auth.招聘需求配置优先级,
  },
  // {
  //   label: '完成招聘',
  //   actionType: 'FINISH',
  //   permission: Auth.招聘需求完成招聘,
  // },
  {
    label: '中止招聘',
    actionType: GreetModalType.SuspendRecruit,
    width: 600,
    permission: Auth.招聘需求中止招聘,
  },
  {
    label: '新增面试',
    actionType: GreetModalType.AddInterview,
    width: 680,
    permission: Auth.招聘需求新增面试,
    request: async ({ executeRequest, params, selectKeys }) => {
      await executeRequest(RequestName.AddInterview, {
        id: selectKeys?.[0],
        ...params,
      });
    },
  },
];

const tabMap = {
  1: 'RECRUIT_CONFIRM',
  2: 'ALL',
};

const checkTabMap = {
  1: 'AUDITING',
  2: 'AUDITED',
};

type TProps = {
  /** 是否为审核 */
  isAudit?: boolean;
};

export default renderPage<TProps>(function DemandManage({ isAudit }) {
  const [form] = Form.useForm();
  const [tabKey, setTabKey] = useState<string>('1');
  const [selectKeys, setSelectKeys] = useState<Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const navigate = useGlobalNavigate();
  const { show } = useGreetModal();
  const [service, executeRequest] = Service();
  const actionRef = useRef<ActionType>(null);
  const [searchParams, setSearchParams] = useQuerySearchParams();

  const currentTabKey = useMemo(() => {
    return searchParams?.tabKey ?? tabKey;
  }, [searchParams?.tabKey, tabKey]);

  useEffect(() => {
    executeRequest(RequestName.GetProvinceCity);
    executeRequest(RequestName.GetOrganizations);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (Object.keys(searchParams || {})?.length) {
      form.setFieldsValue({ ...searchParams });
    }
  }, [form, searchParams]);

  const commonConfig = getCommonConfig({ search: { form } });

  const columns = getColumns({
    searchColumns: [
      getOrganizationTreeColumn({ options: service?.organizationOptions }),
      {
        title: '区域',
        colSize: 1.5,
        dataIndex: 'area',
        valueType: 'select',
        fieldProps: {
          options: service?.areas || [],
          showSearch: true,
        },
      },
      ...searchColumns.filter((v) => (currentTabKey === '1' ? v.dataIndex !== 'status' : true)),
    ],
    tableColumns: [
      ...getTableColumns({
        extraColumns:
          searchParams?.tabKey === '1'
            ? []
            : [
                {
                  title: '中止原因类型',
                  dataIndex: 'firstType',
                  width: 120,
                  valueEnum: {
                    [SuspendType.RECOMMIT]: SuspendTypeCN?.[SuspendType.RECOMMIT],
                    [SuspendType.NO_NEED]: SuspendTypeCN?.[SuspendType.NO_NEED],
                    [SuspendType.SHOP]: SuspendTypeCN?.[SuspendType.SHOP],
                    [SuspendType.OTHER]: SuspendTypeCN?.[SuspendType.OTHER],
                  },
                },
                {
                  title: '中止原因',
                  dataIndex: 'suspendReasonType',
                  ellipsis: true,
                  width: 140,
                  valueEnum: {
                    [SuspendReasonType.RECOMMIT]: SuspendReasonTypeCN?.[SuspendReasonType.RECOMMIT],
                    [SuspendReasonType.NO_NEED_1]: SuspendReasonTypeCN?.[SuspendReasonType.NO_NEED_1],
                    [SuspendReasonType.NO_NEED_2]: SuspendReasonTypeCN?.[SuspendReasonType.NO_NEED_2],
                    [SuspendReasonType.NO_NEED_3]: SuspendReasonTypeCN?.[SuspendReasonType.NO_NEED_3],
                    [SuspendReasonType.SHOP_1]: SuspendReasonTypeCN?.[SuspendReasonType.SHOP_1],
                    [SuspendReasonType.SHOP_2]: SuspendReasonTypeCN?.[SuspendReasonType.SHOP_2],
                    [SuspendReasonType.OTHER]: SuspendReasonTypeCN?.[SuspendReasonType.OTHER],
                  },
                },
              ],
      }),
      {
        title: '操作',
        width: 120,
        fixed: 'right',
        render: (_, record) => (
          <div className="flex gap-x-3">
            {!(isAudit && currentTabKey === '2') && (
              <Permission permission={Auth.招聘需求驳回}>
                {record?.status === 'RECRUIT_CONFIRM' && (
                  <a
                    onClick={() => {
                      show({
                        type: GreetModalType.Reject,
                        width: 500,
                        request: async (params) => {
                          return executeRequest(RequestName.RejectRecruit, { ...params, id: record?.id }).then(() => {
                            message.success('驳回成功');
                            actionRef?.current?.reload();
                          });
                        },
                      });
                    }}
                  >
                    驳回
                  </a>
                )}
              </Permission>
            )}
            {!isAudit && currentTabKey === '2' && (
              <Permission permission={Auth.修改招聘需求}>
                {
                  <a
                    onClick={() => {
                      show({
                        type: GreetModalType.Modify,
                        width: 500,
                        data: { ...record, tempFlag: record?.tempFlag ? String(record.tempFlag) : undefined },
                        request: async (params) => {
                          return executeRequest(RequestName.Modify, {
                            area: params?.district
                              ? `${params?.province}-${params?.city}-${params?.district}`
                              : `${params?.province}-${params?.city}`,
                            tempFlag: params?.tempFlag ? Number(params.tempFlag) : undefined,
                            id: record.id,
                          }).then(() => {
                            message.success('修改成功');
                            actionRef?.current?.reload();
                          });
                        },
                      });
                    }}
                  >
                    修改
                  </a>
                }
              </Permission>
            )}

            {isAudit && currentTabKey === '1' && (
              <Permission permission={Auth.招聘审核操作}>
                <a
                  className="text-red-500"
                  onClick={() => {
                    show({
                      type: GreetModalType.BatchAudit,
                      width: 600,
                      initValue: { shopId: record?.shopId, id: record?.id },
                      request: async (params) => {
                        await executeRequest(RequestName.RecruitAudit, { ...params, idList: [record.id] });
                        message.success('操作成功');
                        setSelectKeys([]);

                        setTimeout(() => {
                          actionRef.current?.reload();
                          actionRef.current?.clearSelected();
                        }, 150);
                      },
                    });
                  }}
                >
                  审核
                </a>
              </Permission>
            )}
            <a
              onClick={() => {
                navigate(isAudit ? RouteKey.RADemandAuditDetail : RouteKey.RADemandDetail, {
                  searchParams: {
                    id: record?.id,
                  },
                });
              }}
            >
              详情
            </a>
          </div>
        ),
      },
    ],
  });

  const tableExtraRender = isAudit
    ? null
    : () => {
        const options =
          currentTabKey === '1'
            ? allButtons.filter((v) => ['CONFIRM', GreetModalType.ConfigPriority].includes(v.actionType))
            : allButtons;

        return (
          <ProCard>
            <div className="flex gap-x-3">
              {options.map((v) => (
                <Permission permission={v.permission} key={v.actionType}>
                  <Button
                    type="primary"
                    disabled={!selectKeys.length}
                    onClick={() => {
                      const onSuccess = (msgText?: string) => {
                        message.success(msgText ?? '操作成功');
                        setSelectKeys([]);

                        setTimeout(() => {
                          actionRef.current?.reload();
                          actionRef.current?.clearSelected();
                        }, 150);
                      };

                      if (['CONFIRM', 'FINISH'].includes(v.actionType)) {
                        const isConfirmRecruit = v.actionType === 'CONFIRM';

                        showConfirmModal({
                          title: isConfirmRecruit ? '确认招聘' : '完成招聘',
                          content: isConfirmRecruit ? '是否确认这些招聘需求？' : '是否将这些招聘需求设置为已完成？',
                          onOk: async () => {
                            await executeRequest(
                              isConfirmRecruit ? RequestName.BatchConfirmRecruit : RequestName.BatchRecruitFinish,
                              {
                                idList: selectKeys,
                                status: !isConfirmRecruit ? 'COMPLETED' : undefined,
                              },
                            );
                            onSuccess();
                          },
                        });

                        return;
                      }

                      if (v.actionType === GreetModalType.AddInterview) {
                        const isSingular = selectedRows.length === 1;
                        const isRecruiting = selectedRows[0]?.status === 'RECRUITING';

                        if (!isSingular && !isRecruiting) {
                          return message.error('仅可选择1条招聘中的需求新增面试');
                        }

                        if (!isSingular) {
                          return message.error('仅可选择1条数据，请重新选择');
                        }

                        if (!isRecruiting) {
                          return message.error('仅招聘中状态需求可以新增面试，请重新选择');
                        }
                      }

                      show({
                        type: v.actionType as GreetModalType,
                        request: async (params) => {
                          if (v.actionType === GreetModalType.AddInterview) {
                            await v.request({ executeRequest, params, selectKeys });
                            onSuccess('面试新增成功');

                            return;
                          }

                          const isPriority = v.actionType === GreetModalType.ConfigPriority;

                          console.log('params', params);

                          await executeRequest(
                            isPriority ? RequestName.BatchConfigPriority : RequestName.BatchRecruitFinish,
                            {
                              idList: selectKeys,
                              privilege: isPriority ? params?.privilege : undefined,
                              status: !isPriority ? 'SUSPENDED' : undefined,
                              suspendReasonType: !isPriority
                                ? ['NO_NEED', 'SHOP'].includes(params?.suspendReasonType)
                                  ? params?.suspendReasonSubType
                                  : params?.suspendReasonType
                                : undefined,
                              suspendRemark: !isPriority ? params?.suspendRemark : undefined,
                            },
                          );
                          onSuccess();
                        },
                        width: v?.width,
                      });
                    }}
                  >
                    {v.label}
                  </Button>
                </Permission>
              ))}
              {currentTabKey === '2' && (
                <Permission permission={Auth.招聘需求批量导入面试}>
                  <Button
                    type="primary"
                    onClick={() => {
                      showConfirmModal({
                        title: '批量导入面试',
                        width: 400,
                        okText: '提交',
                        closable: true,
                        content: (
                          <BatchImportInterview
                            executeRequest={executeRequest}
                            closeModal={() => {
                              Modal.destroyAll();
                            }}
                          />
                        ),
                        footer: null,
                      });
                    }}
                  >
                    批量导入面试
                  </Button>
                </Permission>
              )}
              <Permission permission={Auth.招聘需求导出}>
                <Button
                  type="primary"
                  loading={service?.exportLoading}
                  onClick={() => {
                    executeRequest(RequestName.ExportDemandManageList, formatParams(form?.getFieldsValue())).then(
                      (res) => {
                        res && windowOpen(res);
                      },
                    );
                  }}
                >
                  导出
                </Button>
              </Permission>
            </div>
          </ProCard>
        );
      };

  const formatParams = ({
    current,
    pageSize,
    submitTime,
    status,
    shopId,
    shopName,
    shopOwnerName,
    numRange,
    afterAdjustRequireNum,
    tempFlag,
    ...restPrams
  }) => {
    const [province, city] = restPrams?.area ? restPrams.area?.split(' ') : [];
    const [submitTimeStart, submitTimeEnd] = submitTime?.length ? submitTime : [];
    const params = {
      pageNo: current || 1,
      pageSize: pageSize || 20,
      ...restPrams,
      province,
      city,
      tempFlag: tempFlag ? Number(tempFlag) : undefined,
      tab: isAudit ? checkTabMap[currentTabKey] : tabMap[currentTabKey],
      status: currentTabKey === '1' ? undefined : status,
      shopId: formatFormStr(shopId),
      shopName: formatFormStr(shopName),
      shopOwnerName: formatFormStr(shopOwnerName),
      submitTimeStart: submitTimeStart ? formatDateToUTC(dayjs(submitTimeStart).startOf('day')) : undefined,
      submitTimeEnd: submitTimeEnd ? formatDateToUTC(dayjs(submitTimeEnd).endOf('day')) : undefined,
      requireNumMin: numRange?.[0] || 0,
      requireNumMax: numRange?.[1],
      afterAdjustRequireNumBegin: afterAdjustRequireNum?.[0] || 0,
      afterAdjustRequireNumEnd: afterAdjustRequireNum?.[1],
    };

    return params;
  };

  return (
    <>
      <Tabs
        className="bg-white px-6 pt-4"
        items={[
          { label: isAudit ? '待处理' : '招聘待确认', key: '1' },
          { label: isAudit ? '已处理' : '全部', key: '2' },
        ]}
        activeKey={currentTabKey}
        onChange={(activeKey) => {
          setTabKey(activeKey);
          setSelectKeys([]);
          actionRef.current?.reload(true);
          actionRef.current?.clearSelected();
          setSearchParams({ ...searchParams, tabKey: activeKey });
        }}
      />
      <ProTable
        rowKey="id"
        className="table-hidden-sticky"
        actionRef={actionRef}
        sticky={{ offsetHeader: isInIcestark() ? 0 : 56 }}
        {...commonConfig}
        scroll={{ x: 3000 }}
        columns={columns}
        tableExtraRender={tableExtraRender}
        rowSelection={
          isAudit
            ? undefined
            : {
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectKeys(selectedRowKeys);
                  setSelectedRows(selectedRows);
                },
              }
        }
        request={async (params: any) => {
          const values = formatParams({ ...form.getFieldsValue(), ...params });

          const res = await executeRequest(
            isAudit ? RequestName.GetRecruitAuditList : RequestName.GetDemandManageList,
            values,
          );

          setSearchParams({ tabKey: currentTabKey, ...values });

          return {
            data: res?.data || [],
            total: res?.total || 0,
          };
        }}
      />
    </>
  );
});
