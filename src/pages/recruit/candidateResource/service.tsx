/* eslint-disable react-refresh/only-export-components */
import useServiceReducer from '@/hooks/use-service-reducer';
import HttpAop from '@/http/aop';
import { getProvinceCity } from '@/http/apis/center-control';
import { getCandidatePoolList, updatePhone } from '@/http/apis/demandManage';

export enum RequestName {
  GetProvinceCity,
  EditCandidate,
  GetCandidatePoolList,
}

export const initState = {};

const Service = () => {
  const [service, executeRequest, dispatch] = useServiceReducer(initState, {
    [RequestName.GetProvinceCity]: {
      request: HttpAop(getProvinceCity, {
        after: [
          (data) => {
            return Promise.resolve(
              data.map(({ province, city }) => {
                return {
                  label: `${province} ${city}`,
                  value: `${province} ${city}`,
                };
              }),
            );
          },
        ],
      }),
      afterRequest: (data, dispatch) => {
        dispatch({ areas: data });
      },
    },
    [RequestName.GetCandidatePoolList]: {
      request: getCandidatePoolList,
    },
    [RequestName.EditCandidate]: {
      request: updatePhone,
    },
  });

  return [service, executeRequest, dispatch];
};

export default Service;
