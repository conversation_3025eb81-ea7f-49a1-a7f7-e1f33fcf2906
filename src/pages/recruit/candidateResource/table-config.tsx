import { ProColumns } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import { jobTypeValueMap } from '../demandManage/table-config';

const searchColumns: ProColumns[] = [
  {
    title: '门店编号',
    dataIndex: 'shopId',
    colSize: 1.5,
  },
  {
    title: '门店名称',
    dataIndex: 'shopName',
    colSize: 1.5,
  },
  {
    title: '岗位类型',
    dataIndex: 'jobType',
    valueType: 'select',
    valueEnum: jobTypeValueMap,
    colSize: 1.5,
  },
  {
    title: '候选人手机号',
    dataIndex: 'candidatePhone',
    colSize: 1.5,
  },
  {
    title: '候选人名称',
    dataIndex: 'candidateName',
    colSize: 1.5,
  },
  {
    title: '性别',
    dataIndex: 'gender',
    valueType: 'select',
    valueEnum: {
      0: '女',
      1: '男',
    },
  },
];

const tableColumns: ProColumns[] = [
  {
    title: '序号',
    width: 80,
    dataIndex: 'index',
    fixed: 'left',
    render(_d, _e, index, action) {
      const { pageInfo } = action;
      const { current, pageSize } = pageInfo;

      return (current - 1) * pageSize + index + 1;
    },
  },
  {
    title: '候选人ID',
    width: 120,
    dataIndex: 'candidateId',
    fixed: 'left',
  },
  {
    title: '候选人名称',
    width: 120,
    dataIndex: 'candidateName',
    fixed: 'left',
  },
  {
    title: '候选人标签',
    width: 120,
    dataIndex: 'tags',
    valueEnum: {
      NOT_CANDIDATE: '非候选人',
      RE_COMMENT: '可二次推荐',
      HAS_JOB: '已找到工作',
    },
    fixed: 'left',
  },
  {
    title: '意向城市',
    dataIndex: 'expectLocationName',
  },
  {
    title: '意向区县',
    width: 200,
    dataIndex: 'expectDistrictName',
  },
  {
    title: '意向门店编号',
    dataIndex: 'shopId',
  },

  {
    title: '门店名称',
    dataIndex: 'shopName',
  },
  {
    title: '意向岗位',
    width: 160,
    dataIndex: 'expectPositionName',
  },
  {
    title: '岗位类型',
    dataIndex: 'jobType',
    valueEnum: jobTypeValueMap,
  },
  // {
  //   title: '真实姓名',
  //   dataIndex: 'candidateRealName',
  // },
  {
    title: '手机号',
    width: 130,
    dataIndex: 'phone',
  },
  {
    title: '性别',
    dataIndex: 'geekGender',
    valueEnum: {
      0: '女',
      1: '男',
    },
  },

  {
    title: '年龄',
    dataIndex: 'ageDesc',
  },
  // {
  //   title: '主动/被动',
  //   width: 80,
  //   dataIndex: 'activted',
  //   valueEnum: {
  //     true: '主动',
  //     false: '被动',
  //   },
  // },
  {
    title: '期望薪资',
    dataIndex: 'salary',
  },
  {
    title: '学历',
    dataIndex: 'degreeName',
  },
  {
    title: '学校',
    dataIndex: 'school',
  },
  {
    title: '专业',
    dataIndex: 'major',
  },
  {
    title: '创建时间',
    dataIndex: 'createAt',
    render: (_, { createAt }) => {
      return createAt ? dayjs(createAt).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
  {
    title: '更新时间',
    dataIndex: 'updateAt',
    render: (_, { updateAt }) => (updateAt ? dayjs(updateAt).format('YYYY-MM-DD HH:mm:ss') : '-'),
  },
];

export { searchColumns, tableColumns };
