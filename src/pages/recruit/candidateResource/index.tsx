import { FC, useEffect } from 'react';
import { ProTable } from '@ant-design/pro-components';
import { isInIcestark } from '@ice/stark-app';
import { DatePickerProps, Form } from 'antd';
import dayjs from 'dayjs';
import Service, { RequestName } from './service';
import { searchColumns, tableColumns } from './table-config';
import { getColumns, getCommonConfig, getDateRangeColumn } from '@/components/pro-table-config';
import { formatDateToUTC } from '@/utils/date';
import formatFormStr from '@/utils/formatFormStr';

const disabled30DaysDate: DatePickerProps['disabledDate'] = (current, { from }) => {
  if (from) {
    return Math.abs(current.diff(from, 'days')) >= 30;
  }
};

const CandidateResource: FC = () => {
  const [form] = Form.useForm();
  const [service, executeRequest] = Service();

  useEffect(() => {
    executeRequest(RequestName.GetProvinceCity);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const commonConfig = getCommonConfig({ search: { form }, options: true });

  const columns = getColumns({
    searchColumns: [
      {
        title: '区域',
        colSize: 1.5,
        dataIndex: 'area',
        valueType: 'select',
        fieldProps: {
          options: service?.areas || [],
          showSearch: true,
        },
      },
      ...searchColumns,
      getDateRangeColumn(null, {
        title: '创建时间',
        initialValue: [dayjs().add(-29, 'day').startOf('day'), dayjs().endOf('day')],
        fieldProps: {
          format: 'YYYY-MM-DD',
          showTime: false,
          disabledDate: disabled30DaysDate,
        },
      }),
      {
        title: '候选人标签',
        dataIndex: 'tag',
        valueType: 'select',
        valueEnum: {
          NOT_CANDIDATE: '非候选人',
          RE_COMMENT: '可二次推荐',
          HAS_JOB: '已找到工作',
        },
      },
    ],
    tableColumns,
  });

  return (
    <ProTable
      rowKey="id"
      className="table-hidden-sticky"
      {...commonConfig}
      columns={columns}
      sticky={{ offsetHeader: isInIcestark() ? 0 : 56 }}
      scroll={{ x: 3000 }}
      request={async ({
        current,
        shopId,
        shopName,
        candidateName,
        candidatePhone,
        dateRange,
        loginAccountName,
        ...restPrams
      }) => {
        console.log('********');

        const [province, city] = restPrams?.area ? restPrams.area?.split(' ') : [];

        const params = {
          pageNo: current,
          province,
          city,
          shopId: formatFormStr(shopId),
          startTime: dateRange?.[0] && formatDateToUTC(dayjs(dateRange?.[0])?.startOf('day')),
          endTime: dateRange?.[1] && formatDateToUTC(dayjs(dateRange?.[1])?.endOf('day')),
          shopName: formatFormStr(shopName),
          candidateName: formatFormStr(candidateName),
          candidatePhone: formatFormStr(candidatePhone),
          loginAccountName: formatFormStr(loginAccountName),
          ...restPrams,
        };

        const res = await executeRequest(RequestName.GetCandidatePoolList, params);

        return {
          data: res?.result || [],
          total: res?.total || 0,
        };
      }}
    />
  );
};

export default CandidateResource;
