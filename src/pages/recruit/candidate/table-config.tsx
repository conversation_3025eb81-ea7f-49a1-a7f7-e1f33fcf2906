/* eslint-disable react-refresh/only-export-components */
import { useEffect } from 'react';
import { ActionType, ProColumns, ProForm } from '@ant-design/pro-components';
import { useModal } from '@tastien/thooks';
import { useRequest } from 'ahooks';
import { Button, message, Modal, Popconfirm } from 'antd';
import dayjs from 'dayjs';
import AddInterviewInfo from '../greet/hooks/components/AddInterviewInfo';
import { InterviewStatus, InterviewStatusCN, JobTypeCN } from '@/constants/interview';
import { deleteInterviewInfo, updateInterviewInfo } from '@/http/apis/interview';

export const searchColumns: ProColumns[] = [
  {
    title: '门店编号',
    dataIndex: 'shopId',
    colSize: 1,
  },
  {
    title: '门店名称',
    dataIndex: 'shopName',
    colSize: 1,
  },
  {
    title: '店长',
    dataIndex: 'shopOwnerName',
    colSize: 1,
  },
  {
    title: '岗位类型',
    dataIndex: 'jobType',
    colSize: 1,
    valueType: 'select',
    valueEnum: JobTypeCN,
  },
  {
    title: '候选人名称',
    dataIndex: 'candidateName',
    colSize: 1,
  },
  {
    title: '候选人手机号',
    dataIndex: 'candidatePhone',
    colSize: 1,
  },
  {
    title: '真实姓名',
    dataIndex: 'realName',
    colSize: 1,
  },
  {
    title: '状态',
    dataIndex: 'status',
    colSize: 1,
    valueType: 'select',
    valueEnum: InterviewStatusCN,
  },
  {
    title: '主动/被动',
    dataIndex: 'activted',
    colSize: 1,
    valueType: 'select',
    valueEnum: {
      true: '主动',
      false: '被动',
    },
  },
  {
    title: '是否全职',
    dataIndex: 'fullTime',
    colSize: 1,
    valueType: 'select',
    valueEnum: {
      true: '全职',
      false: '兼职',
    },
  },
];

export const commonColumns: ProColumns[] = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    render(_d, _e, index, action) {
      const { pageInfo } = action;
      const { current, pageSize } = pageInfo;

      return (current - 1) * pageSize + index + 1;
    },
  },
  {
    title: '意向省份',
    dataIndex: 'province',
    width: 100,
  },
  {
    title: '意向城市',
    dataIndex: 'city',
    width: 100,
  },
  {
    title: '意向区域',
    dataIndex: 'district',
    width: 100,
  },
  {
    title: '意向门店编码',
    dataIndex: 'shopId',
    width: 120,
  },
  {
    title: '意向门店名称',
    dataIndex: 'shopName',
    ellipsis: true,
    width: 120,
  },
  {
    title: '岗位类型',
    dataIndex: 'jobType',
    valueEnum: JobTypeCN,
    width: 100,
  },
  {
    title: '是否全职',
    dataIndex: 'fullTime',
    width: 100,
    valueEnum: {
      true: '全职',
      false: '兼职',
    },
  },
  {
    title: '店长',
    dataIndex: 'shopOwnerName',
    width: 100,
  },
  {
    title: '候选人ID',
    dataIndex: 'candidateId',
    width: 120,
  },
  {
    title: '候选人名称',
    dataIndex: 'candidateName',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    valueEnum: InterviewStatusCN,
    width: 140,
  },
  {
    title: '真实姓名',
    dataIndex: 'realName',
    width: 100,
  },
  {
    title: '手机号',
    dataIndex: 'candidatePhone',
    width: 140,
  },
  {
    title: '主动/被动',
    dataIndex: 'activted',
    width: 100,
    valueEnum: {
      true: '主动',
      false: '被动',
    },
  },
  {
    title: '拨打电话次数',
    dataIndex: 'callNum',
    width: 120,
  },
  {
    title: '最新拨打电话时间',
    dataIndex: 'callUpdateTime',
    width: 160,
    render: (_, record) => record?.callUpdateTime && dayjs(record?.callUpdateTime).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '面试日期',
    dataIndex: 'interviewDate',
    width: 120,
    render: (_, record) => record?.interviewDate && dayjs(record?.interviewDate).format('YYYY-MM-DD'),
  },
  {
    title: '面试时间',
    dataIndex: 'interviewTime',
    width: 120,
  },
  {
    title: '面试结果备注',
    dataIndex: 'remark',
    ellipsis: true,
    width: 180,
    render: (_, record) => (record?.status !== InterviewStatus.APPOINTMENT_CANCELED ? record?.remark : '-'),
  },
  {
    title: '面试取消原因',
    dataIndex: 'interviewCancelReason',
    ellipsis: true,
    width: 140,
  },
  {
    title: '面试取消备注',
    dataIndex: 'remark',
    ellipsis: true,
    width: 180,
    render: (_, record) => (record?.status === InterviewStatus.APPOINTMENT_CANCELED ? record?.remark : '-'),
  },
  {
    title: '入职日期',
    dataIndex: 'entryTime',
    width: 120,
    render: (_, record) => record?.entryTime && dayjs(record?.entryTime).format('YYYY-MM-DD'),
  },
  {
    title: '入职是否全职',
    dataIndex: 'candidateFullTime',
    width: 80,
    render: (_, record) => {
      if (record?.candidateFullTime === true) return '全职';

      if (record?.candidateFullTime === false) return '兼职';

      return '-';
    },
  },
  {
    title: '入职备注',
    dataIndex: 'entryRemark',
    ellipsis: true,
    width: 180,
  },
  {
    title: '入职取消原因',
    dataIndex: 'entryCancelReason',
    ellipsis: true,
    width: 140,
  },
  {
    title: '入职取消备注',
    dataIndex: 'cancelRemark',
    ellipsis: true,
    width: 180,
    render: (_, record) => (record?.status === InterviewStatus.ENTRY_CANCELED ? record?.cancelRemark : '-'),
  },
  {
    title: '期望薪资',
    dataIndex: 'salary',
    ellipsis: true,
    width: 120,
  },
  {
    title: '性别',
    dataIndex: 'geekGender',
    width: 60,
    valueEnum: {
      0: '女',
      1: '男',
      '-1': '不限',
    },
  },
  {
    title: '年龄',
    dataIndex: 'ageDesc',
    width: 60,
  },
  {
    title: '学历',
    dataIndex: 'degreeName',
    width: 80,
  },
  {
    title: '意向岗位',
    dataIndex: 'expectPositionName',
    width: 120,
  },
  {
    title: '学校',
    dataIndex: 'school',
    width: 140,
  },
  {
    title: '专业',
    dataIndex: 'major',
    width: 140,
  },
  {
    title: 'BOSS招聘账号ID',
    dataIndex: 'accountId',
    width: 140,
  },
  {
    title: 'BOSS招聘账号名称',
    dataIndex: 'baseName',
    width: 140,
  },
  {
    title: 'BOSS招聘岗位ID',
    dataIndex: 'jobId',
    width: 140,
  },
  {
    title: 'BOSS招聘岗位名称',
    dataIndex: 'jobName',
    width: 140,
  },
  {
    title: '创建时间',
    dataIndex: 'createAt',
    width: 180,
    render: (_, record) => record?.createAt && dayjs(record?.createAt).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '更新人',
    dataIndex: 'updateUserName',
    width: 140,
  },
  {
    title: '更新时间',
    dataIndex: 'updateAt',
    width: 180,
    render: (_, record) => record?.updateAt && dayjs(record?.updateAt).format('YYYY-MM-DD HH:mm:ss'),
  },
];

export function getTableColumns({ actionRef }: { actionRef: React.MutableRefObject<ActionType> }) {
  return [
    ...commonColumns,
    {
      title: '操作',
      width: 120,
      fixed: 'right',
      render: (_, record) => <ActionHandle record={record} actionRef={actionRef} />,
    },
  ] as ProColumns[];
}

function ActionHandle({
  record,
  actionRef,
}: {
  record: Record<string, any>;
  actionRef: React.MutableRefObject<ActionType>;
}) {
  const [form] = ProForm.useForm();

  const formatParams = (values: any) => {
    return {
      candidateName: values?.candidateName,
      candidatePhone: values?.candidatePhone,
      gender: values?.geekGender,
      age: +values?.ageDesc?.match(/\d+/g)?.[0],
      degreeName: values?.degreeName,
      wxNo: values?.wxNo,
      expectPositionName: values?.expectPositionName,
    };
  };

  const { runAsync, loading } = useRequest(
    async (values) => {
      const res = await updateInterviewInfo({
        id: record?.id,
        ...values,
      });

      return res;
    },
    {
      manual: true,
    },
  );

  const modalProps = useModal();

  const { runAsync: deleteRunAsync, loading: deleteLoading } = useRequest(
    async () => {
      const res = await deleteInterviewInfo({ id: record?.id });

      return res;
    },
    { manual: true },
  );

  useEffect(() => {
    if (!modalProps.visible || !record) {
      form.resetFields();
    }

    if (modalProps.visible && record && record?.manual) {
      form.setFieldsValue({
        ...formatParams({ ...record, degreeName: record?.degreeName ? record.degreeName : undefined }),
      });
    }
  }, [form, modalProps.visible, record]);

  if (!record?.manual) {
    return null;
  }

  return (
    <div className="flex gap-x-3">
      {![InterviewStatus.ENTRYED, InterviewStatus.APPOINTMENT_CANCELED, InterviewStatus.ENTRY_CANCELED].includes(
        record?.status,
      ) && (
        <>
          <Button
            className="px-0"
            type="link"
            loading={loading}
            disabled={loading}
            onClick={() => {
              modalProps.openModal();
            }}
          >
            编辑
          </Button>
          <Modal {...modalProps} title="编辑面试候选人" footer={null} width={780} onCancel={modalProps.closeModal}>
            <ProForm
              className="py-6"
              form={form}
              layout="horizontal"
              onFinish={async (values) => {
                await runAsync({ ...values, degreeName: values?.degreeName || '' });
                message.success('修改成功');
                actionRef.current?.reload();
                modalProps.closeModal();
              }}
              submitter={{
                render: () => {
                  return [
                    <div className="flex justify-center gap-x-4 mt-6">
                      <Button
                        onClick={() => {
                          modalProps.closeModal();
                        }}
                      >
                        取消
                      </Button>
                      <Button htmlType="submit" type="primary" loading={loading} disabled={loading}>
                        保存
                      </Button>
                    </div>,
                  ];
                },
              }}
            >
              <AddInterviewInfo />
            </ProForm>
          </Modal>
        </>
      )}
      {record?.status === InterviewStatus.WAIT_APPOINTMENT && (
        <Popconfirm
          title="提示"
          description="是否确认删除对应面试数据？"
          onConfirm={async () => {
            await deleteRunAsync();
            message.success('删除成功');
            actionRef.current?.reload();
          }}
          okText="确认"
          cancelText="取消"
        >
          <Button className="px-0" type="link" loading={deleteLoading} disabled={deleteLoading}>
            删除
          </Button>
        </Popconfirm>
      )}
    </div>
  );
}
