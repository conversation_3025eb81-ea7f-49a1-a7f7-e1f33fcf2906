import { useEffect, useState } from 'react';
import { ProForm, ProFormDigit } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { Spin } from 'antd';
import { DefaultOptionType } from 'antd/es/select';
import OperateButton from '../operate-button';
import ItemCard from '@/components/item-card';
import RoleCascader from '@/components/role-cascader';
import { getFoodSafeConfig } from '@/http/apis/system';

// 获取PT小时时间
function getPtHour(ptStr: string) {
  if (!ptStr) {
    return;
  }

  return +ptStr?.match(/\d+/g)?.join('');
}

type FormData = {
  dealTime: string;
  processRoleIds: number[];
  auditOvertimeRejectTime: string;
};

type TProps = {
  /** 角色选项 */
  roleOptions: DefaultOptionType[];
  /** 点击提交回调 */
  onFinish: (values: FormData) => Promise<void>;
};

export default function FoodSafeAuditReportConfig({ roleOptions, onFinish }: TProps) {
  const [form] = ProForm.useForm<FormData>();
  const [isEdit, setIsEdit] = useState<boolean>(false);

  const { data, loading } = useRequest(async () => {
    const res = (await getFoodSafeConfig()) as any;

    return {
      ...res,
      dealTime: getPtHour(res?.dealTime),
      auditOvertimeRejectTime: getPtHour(res?.auditOvertimeRejectTime),
    };
  });

  useEffect(() => {
    if (!data) {
      return;
    }

    form.setFieldsValue(data);
  }, [data]);

  return (
    <>
      <ProForm
        layout="horizontal"
        submitter={false}
        form={form}
        disabled={!isEdit}
        onFinish={async (values) => {
          await onFinish({
            ...values,
            dealTime: values?.dealTime ? `PT${values.dealTime}H` : undefined,
            auditOvertimeRejectTime: values?.auditOvertimeRejectTime
              ? `PT${values.auditOvertimeRejectTime}H`
              : undefined,
          });

          setIsEdit(false);
        }}
      >
        <Spin spinning={loading}>
          <ItemCard title="食安线下稽核任务报备申请设置">
            <ProFormDigit
              label="审核超时升级上级时长（小时）"
              name="dealTime"
              min={1}
              max={999}
              width="xs"
              rules={[{ required: true, message: '请输入' }]}
              fieldProps={{ precision: 0 }}
            />
            <ProForm.Item
              label="审核角色"
              name="processRoleIds"
              rules={[{ required: true, message: '请选择审核角色' }]}
            >
              <RoleCascader
                multiple
                options={roleOptions || []}
                style={{ width: '20.5rem' }}
                placeholder="请选择"
                showSearch
              />
            </ProForm.Item>
            <ProFormDigit
              label="升级后审核超时驳回时长（小时）"
              name="auditOvertimeRejectTime"
              min={1}
              max={999}
              width="xs"
              rules={[{ required: true, message: '请输入' }]}
              fieldProps={{ precision: 0 }}
            />
          </ItemCard>
        </Spin>
      </ProForm>
      <OperateButton
        isEdit={isEdit}
        onSubmit={() => {
          form?.submit();
        }}
        onCancel={() => {
          setIsEdit(false);
        }}
        onEdit={() => {
          setIsEdit(true);
        }}
      />
    </>
  );
}
