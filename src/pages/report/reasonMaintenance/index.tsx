import { useState } from 'react';
import { Tabs } from 'antd';
import { ReasonMaintenance } from './ReasonMaintenance';
import { Auth } from '@/constants/auth';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { usePermission } from '@/hooks/usePermission';

const tabs = [
  {
    key: '1',
    label: '检查项不合格原因维护',
    children: <ReasonMaintenance />,
  },
  {
    key: '2',
    label: '修改稽核任务原因维护',
    permission: Auth['修改稽核任务原因维护'],
    children: <ReasonMaintenance isAudit />,
  },
];

export default function ReasonMaintenanceIndex() {
  const [activeKey, setActiveKey] = useState<string>('1');
  const [searchParams, setSearchParams] = useQuerySearchParams();
  const hasPermission = usePermission();

  return (
    <Tabs
      items={tabs?.filter((f) => !f?.permission || (f?.permission && hasPermission(f.permission)))}
      activeKey={searchParams?.activeKey || activeKey}
      destroyInactiveTabPane
      onChange={(key) => {
        setActiveKey(key);

        // 切tab得重置form表单不进行缓存
        setSearchParams({ activeKey: key });
      }}
    />
  );
}
