import { isNil } from 'lodash';
import useServiceReducer from '@/hooks/use-service-reducer';
import HttpAop from '@/http/aop';
import {
  addReasonContent,
  createAuditUnqualifiedReason,
  disableAuditUnqualifiedReason,
  editAuditUnqualifiedReason,
  enableAuditUnqualifiedReason,
  getAuditUnqualifiedReasonPage,
  getUnqualifiedReasonPage,
  updataReasonContent,
  updataReasonStatus,
} from '@/http/apis/checkItem';

export enum RequestName {
  GetUnqualifiedReasonPage,
  UpdataReasonStatus,
  UpdataReasonContent,
  AddReason,
  GetAuditUnqualifiedReasonPage,
  CreateAuditUnqualifiedReason,
  EnableAuditUnqualifiedReason,
  DisableAuditUnqualifiedReason,
  EditAuditUnqualifiedReason,
}

export const initState: any = {};

const Service = () => {
  const [service, executeRequest, dispatch] = useServiceReducer(initState, {
    [RequestName.GetUnqualifiedReasonPage]: {
      request: getUnqualifiedReasonPage,
    },
    [RequestName.UpdataReasonStatus]: {
      request: updataReasonStatus,
    },
    [RequestName.UpdataReasonContent]: {
      request: updataReasonContent,
    },
    [RequestName.AddReason]: {
      request: addReasonContent,
    },
    [RequestName.GetAuditUnqualifiedReasonPage]: {
      request: HttpAop(getAuditUnqualifiedReasonPage, {
        before: [
          (params: any) => {
            const { value, enabled, ...rest } = params || {};

            return Promise.resolve({
              ...rest,
              reasonName: value,
              status: !isNil(enabled) ? (enabled === 'true' ? 'ENABLE' : 'DISABLE') : undefined,
            });
          },
        ],
        after: [
          (res: any) => {
            return Promise.resolve({
              ...res,
              data: res?.data?.map((item: any) => ({
                id: item?.id,
                value: item?.reasonName,
                enabled: item?.reasonStatus === 'ENABLE',
                reasonType: item?.reasonType,
              })),
            });
          },
        ],
      }),
    },
    [RequestName.CreateAuditUnqualifiedReason]: {
      request: HttpAop(createAuditUnqualifiedReason),
    },
    [RequestName.EnableAuditUnqualifiedReason]: {
      request: HttpAop(enableAuditUnqualifiedReason),
    },
    [RequestName.DisableAuditUnqualifiedReason]: {
      request: HttpAop(disableAuditUnqualifiedReason),
    },
    [RequestName.EditAuditUnqualifiedReason]: {
      request: HttpAop(editAuditUnqualifiedReason),
    },
  });

  return [service, executeRequest, dispatch];
};

export default Service;
