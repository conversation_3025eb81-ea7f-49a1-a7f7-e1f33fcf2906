import { Fragment, useEffect, useRef, useState } from 'react';
import { ActionType, ProCard, ProForm, ProTable } from '@ant-design/pro-components';
import { Button, message, Popconfirm, Space } from 'antd';
import CreateOrEditReasonModal, { EditReasonInitialValues } from './createOrEditReasonModal';
import Service, { RequestName } from './service';
import { Permission } from '@/components/Permission';
import { getColumns, getCommonConfig, onReset } from '@/components/pro-table-config';
import { Auth } from '@/constants/auth';
import useQuerySearchParams from '@/hooks/use-query-search-params';

const statusMap = {
  false: {
    text: '禁用',
    status: 'processing', // 红色
  },
  true: {
    text: '启用',
    status: 'Success', // 绿色
  },
};

const reasonTypeEnum = {
  PERSONALLY: '个人原因',
  SHOP: '门店原因',
};

interface ReasonMaintenanceProps {
  /** 是否为稽核原因 */
  isAudit?: boolean;
}

export const ReasonMaintenance = ({ isAudit }: ReasonMaintenanceProps) => {
  const [_, executeRequest] = Service();
  const [form] = ProForm.useForm();
  const actionRef = useRef<ActionType>();
  const [createOrEditVisible, setCreateOrEditVisible] = useState(false);
  const [editInitialValues, setEditInitialValues] = useState<EditReasonInitialValues>();
  const [searchParams, setSearchParams] = useQuerySearchParams();

  const commonConfig = getCommonConfig({
    search: {
      form,
    },
  });

  const Wrapper = isAudit ? Permission : Fragment;

  const columns = getColumns({
    searchColumns: [
      {
        title: '原因名称',
        dataIndex: 'value',
        fieldProps: {
          maxLength: 40,
        },
      },
      {
        title: '原因类型',
        dataIndex: 'reasonType',
        valueEnum: reasonTypeEnum,
      },
      {
        title: '状态',
        dataIndex: 'enabled',
        valueEnum: {
          false: '禁用',
          true: '启用',
        },
      },
      // 稽核原因才展示原因类型
    ].filter((f) => f.dataIndex !== 'reasonType' || isAudit),
    tableColumns: [
      {
        title: '原因名称',
        dataIndex: 'value',
        ellipsis: true,
      },
      {
        title: '原因类型',
        dataIndex: 'reasonType',
        valueEnum: reasonTypeEnum,
      },
      {
        title: '状态',
        dataIndex: 'enabled',
        valueEnum: statusMap,
      },
      {
        title: '操作',
        dataIndex: 'opr',
        search: false,
        render: (_, record) => {
          const statusText = record?.enabled ? '禁用' : '启用';

          return (
            <Space>
              <Wrapper permission={Auth['修改稽核任务原因维护_编辑']}>
                <a
                  onClick={() => {
                    setCreateOrEditVisible(true);
                    setEditInitialValues(record);
                  }}
                >
                  编辑
                </a>
              </Wrapper>
              <Wrapper permission={Auth['修改稽核任务原因维护_禁/启用']}>
                <Popconfirm
                  title={`确定要${statusText}该不合格原因吗？`}
                  onConfirm={() => {
                    if (isAudit) {
                      return executeRequest(
                        record?.enabled
                          ? RequestName.DisableAuditUnqualifiedReason
                          : RequestName.EnableAuditUnqualifiedReason,
                        { id: record.id },
                      )
                        .then(() => record.id)
                        .then(() => {
                          actionRef?.current?.reload();
                          message.success(`${statusText}成功！`);
                        });
                    }

                    return executeRequest(RequestName.UpdataReasonStatus, { enabled: !record?.enabled, id: record.id })
                      .then(() => record.id)
                      .then(() => {
                        actionRef?.current?.reload();
                        message.success(`${statusText}成功！`);
                      });
                  }}
                >
                  <a>{statusText}</a>
                </Popconfirm>
              </Wrapper>
            </Space>
          );
        },
      },
      // 稽核原因才展示原因类型
    ].filter((f) => f.dataIndex !== 'reasonType' || isAudit),
  });

  /* useEffect(() => {
    form.setFieldsValue({
      value: searchParams?.value,
      enabled: searchParams?.enabled,
    });
  }, []); */

  useEffect(() => {
    if (isAudit && searchParams?.auditParams) {
      form.setFieldsValue(searchParams.auditParams);

      return;
    }

    if (searchParams?.checkItemParams) {
      form.setFieldsValue(searchParams.checkItemParams);
    }
  }, [isAudit]);

  return (
    <>
      <ProTable
        {...commonConfig}
        actionRef={actionRef}
        columns={columns}
        tableRender={(_d, _p, { table }) => {
          return (
            <ProCard
              extra={
                <Wrapper permission={Auth['修改稽核任务原因维护_新增']}>
                  <Button
                    type="primary"
                    onClick={() => {
                      setCreateOrEditVisible(true);
                      setEditInitialValues(undefined);
                    }}
                  >
                    新建
                  </Button>
                </Wrapper>
              }
            >
              {table}
            </ProCard>
          );
        }}
        onReset={() => onReset(form)}
        request={async ({ current, pageSize, ...rest }) => {
          const params = {
            ...(form.getFieldsValue() || {}),
            pageNo: current || 1,
            pageSize: pageSize || 20,
            ...rest,
          };

          setSearchParams({
            ...searchParams,
            ...(isAudit ? { auditParams: params } : { checkItemParams: params }),
          });

          const res = await executeRequest(
            isAudit ? RequestName.GetAuditUnqualifiedReasonPage : RequestName.GetUnqualifiedReasonPage,
            params,
          );

          return {
            data: res?.data || [],
            success: true,
            total: res.total,
          };
        }}
      />

      <CreateOrEditReasonModal
        initialValues={editInitialValues}
        open={createOrEditVisible}
        onCancel={() => setCreateOrEditVisible(false)}
        onSuccess={() => {
          setCreateOrEditVisible(false);
          actionRef.current?.reload();
        }}
        isAudit={isAudit}
      />
    </>
  );
};
