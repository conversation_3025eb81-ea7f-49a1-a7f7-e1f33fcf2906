/* eslint-disable max-lines-per-function */
import { FC, useEffect, useRef, useState } from 'react';
import { DownloadOutlined } from '@ant-design/icons';
import { ActionType, ProCard, ProForm, ProTable } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { Button, Space } from 'antd';
import dayjs from 'dayjs';
import { omit } from 'lodash';
import DisinfectDetail from './components/disinfection-modal-detail';
import Service, { RequestName } from './service';
import {
  getCheckItemAttributeColumn,
  getColumns,
  getCommonConfig,
  getDateRangeColumn,
  getDisinfectionCompanyColumn,
  getDisinfectionTypeColumn,
  getPersonColumn,
  getRectifyStatusColumn,
  getStoreColumn,
  getStoreTypeColumn,
  onReset,
} from '@/components/pro-table-config';
import { CheckItemAttributeTag } from '@/constants/checklist';
import { DisinfectionRectificationStatus, DisinfectionRectificationStatusCN } from '@/constants/disinfection';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { layoutStore, userStore } from '@/mobx';
import { formatDateToUTC } from '@/utils/date';
import { formatUrlObjToObject } from '@/utils/syncToUrl';

const Disinfection: FC = () => {
  const [nextReportId, setNextReportId] = useState<number>();
  const [auditing, setAuditing] = useState<boolean>(false);
  const [nextStart, setNextStart] = useState<any>();
  const detailRef = useRef<any>();
  const actionRef = useRef<ActionType>();
  const [service, executeRequest] = Service();
  const [form] = ProForm.useForm();
  const [params]: any = useQuerySearchParams();
  const { info: initialState } = userStore;

  useEffect(() => {
    executeRequest(RequestName.GetGroupTreeList);
    executeRequest(RequestName.GetDisinfectionEmployeeSimpleList);
    executeRequest(RequestName.GetDisinfectionCompanySimpleList, {
      enable: true,
      filterNoEmployee: true,
    });

    if (params?.store) {
      try {
        const { shopIds, groupId } = JSON.parse(params?.store);

        if (shopIds?.length > 0) {
          executeRequest(RequestName.GetStoreList, {
            fightGroupId: groupId,
            groupType: 2,
            privilegeCode: 1,
          });
        }
      } catch (e) {
        console.log(e);
      }
    }
  }, []);

  // useEffect(() => {
  //   form?.submit();
  // }, [params?.current, params?.pageSize]);

  const commonConfig = getCommonConfig({
    search: {
      form,
    },
  });

  const columns = getColumns({
    searchColumns: [
      getStoreColumn({
        organizationOptions: service?.organizationOptions,
        storeOptions: service?.storeOptions,
        onStoreFocus: (groupId: any) => {
          executeRequest(RequestName.GetStoreList, {
            fightGroupId: groupId,
            groupType: 2,
            privilegeCode: 1,
          });
        },
      }),
      getStoreTypeColumn(null, { dataIndex: 'shopType' }),
      getDisinfectionTypeColumn(),
      getRectifyStatusColumn(
        {
          valueEnum: {
            [DisinfectionRectificationStatus.WAIT_REFORM]:
              DisinfectionRectificationStatusCN[DisinfectionRectificationStatus.WAIT_REFORM],
            [DisinfectionRectificationStatus.WAIT_AUDIT]:
              DisinfectionRectificationStatusCN[DisinfectionRectificationStatus.WAIT_AUDIT],
            [DisinfectionRectificationStatus.REFORMED]:
              DisinfectionRectificationStatusCN[DisinfectionRectificationStatus.REFORMED],
            [DisinfectionRectificationStatus.CANCEL]:
              DisinfectionRectificationStatusCN[DisinfectionRectificationStatus.CANCEL],
          },
        },
        { dataIndex: 'status' },
      ),
      getDisinfectionCompanyColumn({ options: service?.disinfectionCompanyList }),
      getPersonColumn(
        { options: service?.disinfectionEmployeeList },
        { title: '消杀人员', dataIndex: 'disinfectionUserId' },
      ),
      getCheckItemAttributeColumn(null, { dataIndex: 'accentedTermTag' }),
      getDateRangeColumn(null, {
        title: '报告提交时间',
        initialValue: [dayjs().add(-6, 'day').startOf('day'), dayjs().endOf('day')],
        fieldProps: {
          showTime: {
            defaultValue: [dayjs('00:00', 'HH:mm'), dayjs('23:59', 'HH:mm')],
          },
        },
      }),
      {
        title: '数据范围',
        dataIndex: 'onlyMe',
        valueType: 'select',
        fieldProps: {
          options: [
            { label: '全部', value: false },
            { label: '我处理的', value: true },
          ],
        },
      },
    ],
    tableColumns: [
      {
        title: '检查项',
        dataIndex: 'worksheetItemName',
        width: 120,
        fixed: 'left',
      },
      {
        title: '检查分类',
        dataIndex: 'worksheetCategoryName',
        ellipsis: true,
        width: 180,
      },
      {
        title: '检查项属性',
        dataIndex: 'accentedTermTags',
        width: 100,
        render: (_, record) =>
          record?.accentedTermTags?.map((key) => <div className="mb-2">{CheckItemAttributeTag[key]}</div>),
      },
      {
        title: '门店',
        dataIndex: 'shopName',
        width: 120,
        render: (_, record) => {
          return `${record?.shopName} (${record?.shopId})`;
        },
      },
      {
        title: '消杀公司',
        dataIndex: 'disinfectionCompanyName',
        width: 120,
      },

      {
        title: '报告提交时间',
        dataIndex: 'createTime',
        width: 160,
        render: (_v, { createTime }) => dayjs(createTime).format('YYYY-MM-DD HH:mm:ss') || '-',
      },
      {
        title: '整改状态',
        dataIndex: 'status',
        width: 100,
        valueEnum: {
          [DisinfectionRectificationStatus.WAIT_REFORM]: {
            text: DisinfectionRectificationStatusCN[DisinfectionRectificationStatus.WAIT_REFORM],
            status: 'warning',
          },
          [DisinfectionRectificationStatus.WAIT_AUDIT]: {
            text: DisinfectionRectificationStatusCN[DisinfectionRectificationStatus.WAIT_AUDIT],
            status: 'default',
          },
          [DisinfectionRectificationStatus.REFORMED]: {
            text: DisinfectionRectificationStatusCN[DisinfectionRectificationStatus.REFORMED],
            status: 'success',
          },
          [DisinfectionRectificationStatus.CANCEL]: {
            text: DisinfectionRectificationStatusCN[DisinfectionRectificationStatus.CANCEL],
            status: 'error',
          },
        },
      },
      {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        width: 80,
        search: false,
        fixed: 'right',
        render: (_, record) => {
          return (
            <Space>
              <a
                onClick={() => {
                  setAuditing(false);
                  disinfectDetailRun({
                    id: record.id,
                    onlyRead: true,
                  });
                }}
              >
                详情
              </a>
              {(initialState.shUserId === record.createBy || record?.hasAuditPermission) &&
                DisinfectionRectificationStatus.WAIT_AUDIT === record.status && (
                  <a
                    onClick={() => {
                      setAuditing(true);

                      const values = form.getFieldsValue();
                      const { startDate } = formatParams(values);

                      setNextStart(startDate);
                      disinfectDetailRun({
                        id: record.id,
                        onlyRead: false,
                      });
                    }}
                  >
                    审核
                  </a>
                )}
            </Space>
          );
        },
      },
    ],
  });

  const formatParams = (values) => {
    const {
      store,
      current,
      pageSize,
      dateRange,
      shopType,
      disinfectionType,
      accentedTermTag,
      status,
      disinfectionUserId,
      disinfectionCompanyId,
      ...rest
    } = values;

    return {
      ...rest,
      ...store,
      pageSize: pageSize || params?.pageSize || 20,
      pageNo: current || params?.current || 1,
      startDate: dateRange?.[0] && formatDateToUTC(dateRange?.[0]),
      endDate: dateRange?.[1] && formatDateToUTC(dateRange?.[1]),
      shopType,
      disinfectionType,
      accentedTermTag,
      status,
      disinfectionUserId,
      disinfectionCompanyId,
    };
  };
  const { run: nextRun } = useRequest(
    async (params: any) => {
      executeRequest(RequestName.QueryDisinfectNext, params).then((e) => {
        setNextReportId(e);
      });
    },
    {
      manual: true,
      onSuccess: () => {},
    },
  );
  const { run: disinfectDetailRun } = useRequest(
    async (paramsValue: any) => {
      const { id, onlyRead } = paramsValue;

      executeRequest(RequestName.GetDisinfectDetail, id).then((res) => {
        if (res.id && res.status === 'WAIT_AUDIT') {
          const paramNext = {
            ...omit(formatParams(form.getFieldsValue()), ['pageNo', 'pageSize', 'endDate']),
            taskId: res.taskId,
            status: 'WAIT_AUDIT',
            id: res.id,
          };

          !onlyRead && nextRun(paramNext);
        }
      });
    },
    {
      manual: true,
      onSuccess: () => {
        detailRef?.current?.setVisible(true);
      },
    },
  );

  return (
    <>
      <ProTable
        scroll={{ x: 1600 }}
        {...commonConfig}
        actionRef={actionRef}
        columns={columns}
        request={async (values) => {
          const res = await executeRequest(RequestName.GetDisinfectionRectificationList, formatParams(values));

          return {
            data: res?.data,
            total: res?.total,
            success: true,
          };
        }}
        form={{
          syncToUrl: (values: any, type: any) => {
            if (type === 'get') {
              const formatValues = formatUrlObjToObject(values);

              return {
                ...formatValues,
              };
            }

            return {
              ...values,
            };
          },
        }}
        onReset={() => onReset(form, { dateRange: [dayjs().add(-6, 'day').startOf('day'), dayjs().endOf('day')] })}
        tableRender={(_p, _, { table }) => {
          return (
            <ProCard
              extra={
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={() => {
                    const values = form.getFieldsValue();

                    executeRequest(RequestName.ExportDisinfectionRectificationList, formatParams(values)).then(() => {
                      layoutStore.increaseExport();
                    });
                  }}
                >
                  下载明细
                </Button>
              }
            >
              {table}
            </ProCard>
          );
        }}
      />
      <DisinfectDetail
        // @ts-ignore
        ref={detailRef}
        directly={true}
        type="disinfection"
        auditing={auditing}
        tabKey={params?.tabKey ? params?.tabKey : 'Self'}
        realData={service?.disinfectDetail}
        onSuccess={() => {
          form.submit();
        }}
        onNextReview={
          nextReportId
            ? () => {
                disinfectDetailRun({ startDate: nextStart, id: nextReportId, onlyRead: false });
              }
            : undefined
        }
      />
    </>
  );
};

export default Disinfection;
