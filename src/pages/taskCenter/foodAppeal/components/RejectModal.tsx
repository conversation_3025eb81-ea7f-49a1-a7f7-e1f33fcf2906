import { useCallback, useState } from 'react';
import { CloseCircleFilled, PlusOutlined } from '@ant-design/icons';
import { ProForm, ProFormDependency, ProFormTextArea, ProFormUploadDragger } from '@ant-design/pro-components';
import { message, Spin } from 'antd';
import { cloneDeep } from 'lodash';
import MediaCard from '@/components/MediaCard';
import PictureInput from '@/components/picture-input';
import { useOSSClient } from '@/hooks/use-oss-client';
import createModal from '@/utils/antd/createModal';

interface RejectModalProps {
  onFinish: (values: any) => Promise<any>;
}

export const RejectModal = createModal<{}, RejectModalProps>(
  (_p, setVisible, payload) => {
    const [form] = ProForm.useForm();

    const { onFinish } = payload?.data || {};

    const [loading, setLoading] = useState(false);
    const { uploadFile } = useCallback(useOSSClient, [])('PATROL');

    return [
      {
        footer: null,
      },
      <ProForm
        onFinish={async (values) => {
          if (loading) {
            message.error('文件正在上传!');

            return;
          }

          const _payload = {
            remark: values?.remark,
            attachmentIds: values?.itemImages?.map(({ response }: any) => response?.id),
          };

          await onFinish?.(_payload);

          setVisible(false);
        }}
      >
        <ProFormTextArea
          name="remark"
          label="驳回说明"
          placeholder="请输入驳回说明, 最多500字"
          rules={[{ required: true, message: '请输入驳回说明' }]}
          fieldProps={{ maxLength: 500 }}
        />
        <ProForm.Item label="照片" name="itemImages">
          <PictureInput
            onChange={async (file) => {
              try {
                const response = await uploadFile(file, true);

                const itemImages: any = form.getFieldValue('itemImages') || [];

                form.setFieldValue(
                  'itemImages',
                  itemImages?.concat({
                    response,
                    status: 'done',
                    type: response?.contentType,
                    thumbUrl: response?.snapshotUrl,
                    uid: response?.id,
                  }),
                );
              } catch (e) {
                message.error(e);
              }
            }}
          />
          <ProFormUploadDragger
            name="itemImages"
            noStyle
            accept=".jpg,.png,.mp4,.jpeg"
            title={'点击或将文件拖到这里上传'}
            description={'支持.jpg/.png/.mp4/.jpeg格式，视频大小不得超过20M'}
            icon={<PlusOutlined />}
            max={9}
            fieldProps={{
              showUploadList: false,

              customRequest: async (e: any) => {
                try {
                  if (!e?.file?.type?.includes('video') && !e?.file?.type?.includes('image')) {
                    message.error('请上传图片或视频');

                    const itemImages = form.getFieldValue('itemImages');
                    const cloneItemImages = cloneDeep(itemImages);

                    cloneItemImages.pop();

                    form.setFieldValue('itemImages', cloneItemImages);

                    return;
                  }

                  form?.setFieldValue('loading', true);
                  setLoading(true);

                  const response = await uploadFile(e.file, true, null, {});

                  e.onSuccess(response);
                  form?.setFieldValue('loading', false);
                  setLoading(false);
                } catch (error) {
                  e.onError();

                  const itemImages = form.getFieldValue('itemImages');

                  const cloneItemImages = cloneDeep(itemImages);

                  cloneItemImages.pop();

                  form?.setFieldValue('itemImages', cloneItemImages);
                  form?.setFieldValue('loading', false);
                  setLoading(false);
                } finally {
                }
              },
            }}
          />
          <ProFormDependency name={['itemImages', 'loading']}>
            {({ itemImages, loading }) => {
              const images = itemImages?.filter(({ response }) => response).map(({ response }) => response);

              return (
                <div className="flex flex-wrap gap-2">
                  {itemImages?.map(({ response: item }, index) => {
                    return item ? (
                      <div className="relative inline-flex mt-2.5">
                        <CloseCircleFilled
                          className="absolute top-[-8px] right-[-8px] z-10  text-[#ff4d4f]"
                          style={{ fontSize: '16px' }}
                          onClick={() => {
                            form.setFieldValue(
                              'itemImages',
                              itemImages?.filter((_, i) => i !== index),
                            );
                          }}
                        />
                        <MediaCard file={item} key={item.id} fileList={images} width={80} height={80} />
                      </div>
                    ) : undefined;
                  })}
                  {loading && (
                    <Spin tip="上传中">
                      <div className="size-20" />
                    </Spin>
                  )}
                </div>
              );
            }}
          </ProFormDependency>
        </ProForm.Item>
      </ProForm>,
    ];
  },
  {
    title: '驳回',
    destroyOnClose: true,
  },
);
