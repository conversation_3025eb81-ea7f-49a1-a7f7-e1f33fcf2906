/* eslint-disable max-lines-per-function */
import { useEffect, useRef, useState } from 'react';
import { ActionType, ProColumns, ProForm, ProTable } from '@ant-design/pro-components';
import { Tabs } from 'antd';
import dayjs from 'dayjs';
import DetailDrawer, { type DrawerRef } from './components/DetailDrawer';
import { ApplyReasonEnumMap, ApplyStatusEnum, ApplyStatusEnumMap } from './const';
import Service, { RequestName } from './service';
import { MapRouteContext } from '../auditSchedulingManage';
import { CheckRouteButton } from '../auditSchedulingManage/components/CheckRouteButton';
import MapRoute, { type AuditRoute } from '../auditSchedulingManage/components/MapRoute';
import { Permission } from '@/components/Permission';
import { getColumns, getCommonConfig, getPersonColumn, getStoreColumn } from '@/components/pro-table-config';
import { Auth } from '@/constants/auth';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import useUserListCaching from '@/hooks/use-user-list-caching';
import { formatDateToUTC } from '@/utils/date';

enum TabsEnum {
  待处理 = 'wait',
  全部 = 'all',
}

const requestNameMap = {
  [TabsEnum.待处理]: RequestName.GetWaitErrorReportList,
  [TabsEnum.全部]: RequestName.GetErrorReportList,
};

const tabs = [
  { key: TabsEnum.待处理, label: '待处理' },
  { key: TabsEnum.全部, label: '全部' },
];

const initSubmitTime = [dayjs().subtract(1, 'month').startOf('day'), dayjs().endOf('day')];

export default function FoodSafeErrorReportApplyDeal() {
  const [service, executeRequest] = Service();
  const [form] = ProForm.useForm();
  const { userListCachingRef } = useUserListCaching({ options: service?.userOptions });
  const [searchParams, setSearchParams] = useQuerySearchParams();

  const [activeKey, setActiveKey] = useState<string>(searchParams?.activeKey || TabsEnum.待处理);

  const [mapState, setMapState] = useState<{
    visible: boolean;
    currentData?: AuditRoute;
  }>({ visible: false });

  const actionRef = useRef<ActionType>(null);
  const detailDrawerRef = useRef<DrawerRef>(null);

  const commonConfig = getCommonConfig({
    search: {
      form,
    },
  });

  const getRouteIdsByBatchDate = (date: string = dayjs().format('YYYY-MM')) => {
    executeRequest(RequestName.GetRouteIdsByBatchDate, date);
  };

  const openMapDrawer = (val: AuditRoute) => {
    setMapState({
      visible: true,
      currentData: val,
    });
  };

  useEffect(() => {
    executeRequest(RequestName.GetGroupTreeList);
    getRouteIdsByBatchDate();
  }, []);

  useEffect(() => {
    if (Object.keys(searchParams || {})?.length > 0) {
      const { groupId, shopIds, applyBeginTime, applyEndTime, batchDataBatchId, applyUserIds, applyStatus, ...rest } =
        searchParams;

      if (groupId || shopIds) {
        executeRequest(RequestName.GetStoreList, {
          fightGroupId: groupId,
          groupType: 2,
          privilegeCode: 1,
        });
      }

      form.setFieldsValue({
        ...rest,
        batchDataBatchId: batchDataBatchId ? +batchDataBatchId : undefined,
        applyUserIds: applyUserIds?.length ? applyUserIds?.map((m) => +m) : undefined,
        applyStatus: applyStatus?.length ? applyStatus[0] : undefined,
        submitTime: applyBeginTime && applyEndTime ? [applyBeginTime, applyEndTime] : undefined,
        store: groupId || shopIds ? { groupId, shopIds } : undefined,
      });
    }
  }, []);

  const columns = getColumns({
    searchColumns: [
      getStoreColumn({
        organizationOptions: service?.organizationOptions,
        storeOptions: service?.storeOptions,
        onStoreFocus: (groupId: any) => {
          executeRequest(RequestName.GetStoreList, {
            fightGroupId: groupId,
            groupType: 2,
            privilegeCode: 1,
          });
        },
      }),
      getPersonColumn(
        {
          options: userListCachingRef?.current || service?.userOptions,
          onFocus: () => {
            if (!userListCachingRef?.current) {
              executeRequest(RequestName.GetUserInfoList);
            }
          },
          loading: service?.userLoading,
        },
        {
          dataIndex: 'applyUserIds',
          title: '提交人',
          colSize: 1.5,
          fieldProps: { mode: 'multiple' },
        },
      ),
      {
        title: '提交时间',
        dataIndex: 'submitTime',
        valueType: 'dateRange',
        colSize: 2,
        initialValue: initSubmitTime,
        fieldProps: {
          allowClear: false,
        },
      },
      {
        title: '路线ID',
        dataIndex: 'batchDataBatchId',
        valueType: 'select',
        fieldProps: {
          options: service?.routeIds,
          showSearch: true,
        },
        colSize: 1.5,
      },
      {
        title: '审核状态',
        dataIndex: 'applyStatus',
        valueEnum: ApplyStatusEnumMap,
      },
    ].filter((f) => f.dataIndex !== 'applyStatus' || activeKey === TabsEnum.全部) as ProColumns[],
    tableColumns: [
      {
        title: '序号',
        dataIndex: 'id',
        width: 80,
      },
      {
        title: '路线ID',
        dataIndex: 'batchDataBatchId',
      },
      {
        title: '门店ID',
        dataIndex: 'shopId',
      },
      {
        title: '门店名称',
        dataIndex: 'shopName',
      },
      {
        title: '申请人',
        dataIndex: 'applyUserName',
      },
      {
        title: '审核人',
        dataIndex: 'approveUserName',
      },
      {
        title: '申请原因',
        dataIndex: 'applyReason',
        valueEnum: ApplyReasonEnumMap,
        width: 100,
      },
      {
        title: '审核状态',
        dataIndex: 'status',
        valueEnum: ApplyStatusEnumMap,
        width: 100,
      },
      {
        title: '审核截止时间',
        dataIndex: 'approveExpiredTime',
        width: 120,
        render: (_, { approveExpiredTime }) =>
          approveExpiredTime ? dayjs(approveExpiredTime).format('YYYY-MM-DD HH:mm:ss') : '-',
      },
      {
        title: '任务执行时段',
        dataIndex: 'taskBeginTime',
        width: 170,
        render: (_, { taskBeginTime, taskExpiredTime }) =>
          taskBeginTime && taskExpiredTime ? (
            <>
              {dayjs(taskBeginTime).format('YYYY-MM-DD HH:mm:ss')}
              <div>{dayjs(taskExpiredTime).format('YYYY-MM-DD HH:mm:ss')}</div>
            </>
          ) : (
            '-'
          ),
      },
      {
        title: '路线预览',
        dataIndex: 'batchDataBatchId',
        width: 120,
        render: (_, record) =>
          record?.batchDataBatchId ? <CheckRouteButton batchId={record.batchDataBatchId} /> : '-',
      },
      {
        title: '提交时间',
        dataIndex: 'createTime',
        render: (_, { createTime }) => (createTime ? dayjs(createTime).format('YYYY-MM-DD HH:mm:ss') : '-'),
      },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        render: (_, { updateTime }) => (updateTime ? dayjs(updateTime).format('YYYY-MM-DD HH:mm:ss') : '-'),
      },
      {
        title: '操作',
        fixed: 'right',
        width: 120,
        render: (_, record) => (
          <div>
            <Permission permission={Auth['任务中心_食安异常报备处理_审核']}>
              {record?.status === ApplyStatusEnum.待审核 && record?.canApprove && (
                <a
                  className="mr-3"
                  onClick={() => {
                    detailDrawerRef.current.showDrawer({ ...record, isOperate: true });
                  }}
                >
                  审核
                </a>
              )}
            </Permission>
            <a
              onClick={() => {
                detailDrawerRef.current.showDrawer(record);
              }}
            >
              详情
            </a>
          </div>
        ),
      },
    ].filter((f) => f.dataIndex !== 'status' || activeKey === TabsEnum.全部) as ProColumns[],
  });

  return (
    <>
      <MapRouteContext.Provider value={{ openMapDrawer }}>
        <Tabs
          activeKey={searchParams?.activeKey || activeKey}
          items={tabs}
          onChange={(key) => {
            setActiveKey(key);
            setSearchParams({ ...searchParams, activeKey: key });
            form.resetFields();
            actionRef.current?.reload();
          }}
        />
        <ProTable
          rowKey="id"
          {...commonConfig}
          actionRef={actionRef}
          columns={columns}
          pagination={{ showSizeChanger: true }}
          scroll={{ x: 'max-content' }}
          request={async ({ current, pageSize }) => {
            const { store, submitTime, applyStatus, ...rest } = form.getFieldsValue();
            const [applyBeginTime, applyEndTime] = submitTime || [];

            const params = {
              ...rest,
              ...(store || {}),
              pageNo: current ?? 1,
              pageSize: pageSize ?? 20,
              applyStatus: applyStatus ? [applyStatus] : undefined,
              // 待处理tab传死待审核状态
              ...(activeKey === TabsEnum.待处理 ? { applyStatus: [ApplyStatusEnum.待审核] } : {}),
              applyBeginTime: applyBeginTime
                ? formatDateToUTC(dayjs(applyBeginTime).startOf('day'))
                : formatDateToUTC(initSubmitTime[0]),
              applyEndTime: applyEndTime
                ? formatDateToUTC(dayjs(applyEndTime).endOf('day'))
                : formatDateToUTC(initSubmitTime[1]),
            };

            setSearchParams({ ...searchParams, ...params });

            const res = await executeRequest(requestNameMap[activeKey], {
              ...params,
              countFlag: true, // 统计 total 数量
            });

            return {
              data: res?.data,
              total: res?.total,
              success: true,
            };
          }}
        />
        {/* 详情、审核抽屉 */}
        <DetailDrawer
          ref={detailDrawerRef}
          userOptions={service?.userOptions}
          onFetchUserOptions={() => {
            if (!userListCachingRef?.current) {
              executeRequest(RequestName.GetUserInfoList);
            }
          }}
          onReload={async () => {
            await actionRef.current?.reload();
          }}
        />
        <MapRoute
          open={mapState?.visible}
          onClose={() => {
            setMapState({ visible: false });
          }}
          data={mapState?.currentData}
        />
      </MapRouteContext.Provider>
    </>
  );
}
