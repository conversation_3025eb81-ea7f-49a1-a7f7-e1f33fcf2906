export enum TabsEnum {
  待处理 = 'wait',
  全部 = 'all',
}

export const tabs = [
  { key: TabsEnum.待处理, label: '待处理' },
  { key: TabsEnum.全部, label: '全部' },
];

export enum ApplyStatusEnum {
  待审核 = 'WAITING_APPROVE',
  审核通过 = 'APPROVE_PASS',
  审核驳回 = 'APPROVE_REFUSE',
  已取消 = 'CANCEL',
}

export const ApplyStatusEnumMap = {
  WAITING_APPROVE: '待审核',
  APPROVE_PASS: '审核通过',
  APPROVE_REFUSE: '审核驳回',
  CANCEL: '已取消',
};

export enum ApplyReasonEnum {
  个人原因 = 'USER_REASON',
  门店原因 = 'SHOP_REASON',
}

export const ApplyReasonEnumMap = {
  [ApplyReasonEnum.个人原因]: '个人原因',
  [ApplyReasonEnum.门店原因]: '门店原因',
};
