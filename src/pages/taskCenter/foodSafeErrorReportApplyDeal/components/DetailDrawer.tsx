import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import { But<PERSON>, Divider, Drawer, Spin } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import AuditPassConfirmModal, { AuditPassConfirmModalRef } from './AuditPassConfirmModal';
import RejectDrawer from './RejectDrawer';
import { ApplyReasonEnumMap } from '../const';
import MediaCard from '@/components/MediaCard';
import { getErrorReportDetail } from '@/http/apis/task-center';

// 操作记录
/* function OperateRecord({ dataSource }) {
  return (
    <div className="mb-6">
      {dataSource?.map((m, index) => (
        <div className="mt-4" key={index}>
          <div className="flex items-center gap-x-2">
            <div className="size-8 rounded-full bg-blue-400 text-white flex justify-center items-center">
              {m?.username?.slice(0, 1)}
            </div>
            <div className="flex gap-x-3">
              <span>{m?.username}</span>
              <span>修改路线计划信息</span>
            </div>
          </div>
          <div className="bg-gray-100 p-4 ml-10 mt-3">
            <div>执行人：xxx</div>
            <div>执行时段：xxx</div>
            <div className="mt-4 flex gap-3 flex-wrap">
              {Array.from({ length: 10 }, () => (
                <Image width={80} src="https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg" />
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
} */

const infoList: {
  label?: string;
  dataIndex: string;
  render?: (value: any, record: Record<string, any>) => React.ReactNode;
}[] = [
  {
    label: '申请异常原因',
    dataIndex: 'applyReason',
    render: (v) => ApplyReasonEnumMap?.[v],
  },
  {
    label: '具体原因',
    dataIndex: 'applySpecificReason',
  },
  {
    label: '审核截止时间',
    dataIndex: 'approveExpiredTime',
    render: (v) => (v ? dayjs(v).format('YYYY-MM-DD HH:mm:ss') : '-'),
  },
  {
    label: '任务执行时间',
    dataIndex: 'taskBeginTime',
    render: (v, { taskExpiredTime: end }) =>
      v && end ? `${dayjs(v).format('YYYY/MM/DD HH:mm:ss')} - ${dayjs(end).format('YYYY/MM/DD HH:mm:ss')}` : '-',
  },
  {
    dataIndex: 'applyImages',
    render: (v) =>
      v?.length ? (
        <div className="flex flex-wrap my-1.5">
          {v?.map((item) => (
            <div key={item.id} className="my-1.5">
              <MediaCard file={item} fileList={v} width={80} height={80} />
            </div>
          ))}
        </div>
      ) : null,
  },
  {
    label: '备注',
    dataIndex: 'applyRemark',
  },
];

export interface DrawerRef {
  showDrawer: (initValue?: Record<string, any>) => void;
}

export type TProps = {
  /** 转办人员选项 */
  userOptions?: any[];
  /** 获取转办人员选项 */
  onFetchUserOptions: () => void;
  /** 刷新列表 */
  onReload?: () => Promise<void>;
};

export default forwardRef<DrawerRef, TProps>(function DetailDrawer({ userOptions, onFetchUserOptions, onReload }, ref) {
  const [drawerConfig, setDrawerConfig] = useState<{ open: boolean; initValue?: Record<string, any> }>({
    open: false,
  });
  const rejectDrawerRef = useRef<DrawerRef>();
  const auditPassConfirmModalRef = useRef<AuditPassConfirmModalRef>();

  const onClose = () => {
    setDrawerConfig((pre) => ({ ...pre, open: false }));
  };

  const onSuccessAction = () => {
    onClose();

    setTimeout(() => {
      onReload();
    }, 200);
  };

  useEffect(() => {
    if (drawerConfig.open && !userOptions) {
      onFetchUserOptions();
    }
  }, [drawerConfig.open, userOptions]);

  useImperativeHandle(ref, () => ({
    showDrawer: (initValue?: any) => {
      setDrawerConfig({ open: true, initValue });
    },
  }));

  const { id, isOperate } = drawerConfig?.initValue || {};

  const { data: detailData, loading: detailLoading } = useRequest(
    async () => {
      const res = (await getErrorReportDetail(id)) as any;

      return res;
    },
    {
      refreshDeps: [id],
      ready: drawerConfig?.open && !!id,
    },
  );

  const { shopId, shopName, canApprove, ...rest } = detailData || {};

  return (
    <>
      <Drawer
        title={isOperate ? '审核' : '详情'}
        width="40vw"
        open={drawerConfig.open}
        onClose={onClose}
        styles={{ body: { padding: 0 } }}
      >
        {detailLoading ? (
          <div className="flex h-full justify-center items-center">
            <Spin spinning={detailLoading} />
            <span className="ml-3">加载中...</span>
          </div>
        ) : (
          <div className="flex flex-col justify-between h-full">
            <div className="flex flex-col overflow-y-auto p-6 ">
              <div className="font-bold text-base mb-4">申请信息</div>
              <div className="flex gap-x-1.5 font-bold text-base ">
                <span>{shopId}</span>
                <span>{shopName}</span>
              </div>
              <div className="bg-gray-100 p-4 mt-4 flex flex-col">
                {infoList?.map((m, index) => (
                  <div className="flex items-center" key={`${m?.dataIndex}_${index}`}>
                    {!!m?.label && <div className="font-medium leading-6">{m.label}：</div>}
                    <div
                      className={classNames('text-sm', {
                        'max-w-[80%]': m?.label,
                      })}
                    >
                      {m?.render?.(rest?.[m?.dataIndex], rest) ?? rest?.[m?.dataIndex]}
                    </div>
                  </div>
                ))}
              </div>
              <Divider />
              {/* 下个版本才有操作记录 */}
              {/* <div className="font-bold text-base">操作记录</div>
              <OperateRecord dataSource={[data]} />  */}
            </div>
            {isOperate && canApprove && (
              <div>
                <div className="flex gap-x-5 px-6 py-5">
                  <Button
                    className="flex flex-1"
                    onClick={() => {
                      rejectDrawerRef.current?.showDrawer({ ...drawerConfig?.initValue, detailData });
                    }}
                  >
                    驳回
                  </Button>
                  <Button
                    className="flex flex-1"
                    type="primary"
                    onClick={() => {
                      auditPassConfirmModalRef.current?.showModal({ ...drawerConfig?.initValue, detailData });
                    }}
                  >
                    审核通过
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}
      </Drawer>
      {/* 驳回抽屉 */}
      <RejectDrawer ref={rejectDrawerRef} onSuccessAction={onSuccessAction} />
      {/* 审核通过确认弹窗 */}
      <AuditPassConfirmModal
        ref={auditPassConfirmModalRef}
        userOptions={userOptions}
        onSuccessAction={onSuccessAction}
      />
    </>
  );
});
