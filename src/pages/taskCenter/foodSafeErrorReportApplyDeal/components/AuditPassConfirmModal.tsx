import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { EnvironmentOutlined } from '@ant-design/icons';
import { ProForm, ProFormSelect } from '@ant-design/pro-components';
import { useDebounceFn, useRequest } from 'ahooks';
import { message, Modal } from 'antd';
import { TProps } from './DetailDrawer';
import { ApplyReasonEnum } from '../const';
import { ShopStatusCN } from '@/constants/organization';
import { approveReport, getHideShopInfo, getRouteIdsByBatchDate } from '@/http/apis/task-center';

export interface AuditPassConfirmModalRef {
  showModal: (initValue?: Record<string, any>) => void;
}

type AuditPassConfirmModalProps = Pick<TProps, 'userOptions'> & {
  /** 操作成功执行动作 */
  onSuccessAction: () => void;
};

export default forwardRef<AuditPassConfirmModalRef, AuditPassConfirmModalProps>(function AuditPassConfirmModal(
  { userOptions, onSuccessAction },
  ref,
) {
  const [modalProps, setModalProps] = useState<{ open: boolean; initValue?: Record<string, any> }>({
    open: false,
  });
  const [form] = ProForm.useForm();

  const processUserId = ProForm.useWatch('newProcessUserId', form);

  const onClose = () => {
    setModalProps((pre) => ({ ...pre, open: false }));
  };

  useImperativeHandle(ref, () => ({
    showModal: (initValue = {}) => {
      setModalProps({ open: true, initValue });
    },
  }));

  useEffect(() => {
    if (!modalProps?.open) {
      form.resetFields();
    }
  }, [modalProps?.open]);

  const { id: applyId, applyReason, detailData, batchDataBatchId: batchId } = modalProps?.initValue || {};

  // 路线选项
  const { data: routeOptions, loading: routeOptionsLoading } = useRequest(
    async () => {
      const res = (await getRouteIdsByBatchDate(detailData?.batchDate, {
        processUserId,
        statuses: 'PENDING,IN_PROGRESS,COMPLETED',
      })) as any;

      if (!res?.length) {
        return [];
      }

      return res?.map((m) => ({ label: m?.batchId, value: m?.id }));
    },
    {
      refreshDeps: [detailData?.batchDate, processUserId],
      ready: modalProps?.open && !!detailData?.batchDate && !!processUserId,
    },
  );

  const filterUserOptions = useMemo(() => {
    // 过滤当前申请人
    return userOptions?.filter((v) => detailData?.applyUserId !== v?.value);
  }, [detailData?.applyUserId, userOptions]);

  // 隐藏门店信息
  const { data: hideShopInfoData } = useRequest(
    async () => {
      const res = await getHideShopInfo({
        batchId,
        shopId: detailData?.shopId,
      });

      return res;
    },
    {
      refreshDeps: [applyReason, batchId, detailData?.shopId],
      ready: modalProps?.open && applyReason === ApplyReasonEnum.门店原因 && !!batchId && !!detailData?.shopId,
    },
  );

  // 审核通过
  const { runAsync } = useRequest(
    async (values) => {
      const res = await approveReport({
        ...values,
        applyResult: 'APPROVE_PASS',
      });

      return res;
    },
    {
      manual: true,
      onSuccess: () => {
        message.success('操作成功');
        onClose();
        onSuccessAction?.();
      },
    },
  );

  const node = {
    [ApplyReasonEnum.个人原因]: (
      <ProForm form={form} submitter={false}>
        <ProFormSelect
          label="转办人员"
          name="newProcessUserId"
          rules={[{ required: true, message: '请选择转办人员' }]}
          options={filterUserOptions}
          showSearch
        />
        <ProFormSelect
          label="将任务迁移至以下路线"
          name="newBatchId"
          options={routeOptions}
          fieldProps={{ loading: routeOptionsLoading }}
          showSearch
        />
      </ProForm>
    ),
    [ApplyReasonEnum.门店原因]: hideShopInfoData ? (
      <div>
        <div className="font-medium">审核通过后将自动作废此门店稽核任务，并自动新分配以下门店稽核任务：</div>
        <div className="mt-4">
          {hideShopInfoData?.shopId}
          {hideShopInfoData?.shopName}{' '}
          {hideShopInfoData?.shopStatus ? `（${ShopStatusCN[hideShopInfoData.shopStatus]}）` : ''}
        </div>
        <div className="text-gray-500 text-sm mt-3">
          <EnvironmentOutlined className="mr-2" />
          {hideShopInfoData?.shopAddress}
        </div>
      </div>
    ) : (
      <span className="font-medium">
        审核通过后将自动作废此门店稽核任务，此路线隐藏门店已经全部激活，因此不会自动分配新的任务。
      </span>
    ),
  };

  const { run: submitRun } = useDebounceFn(
    async () => {
      const values = await form.validateFields();

      await runAsync({
        applyId,
        ...values,
        ...(applyReason === ApplyReasonEnum.门店原因 ? { newShopId: hideShopInfoData?.shopId } : {}),
      });
    },
    { wait: 300 },
  );

  return (
    <Modal title="审核通过确认" width={400} open={modalProps.open} onCancel={onClose} onOk={submitRun}>
      {node[applyReason]}
    </Modal>
  );
});
