import useServiceReducer from '@/hooks/use-service-reducer';
import HttpAop from '@/http/aop';
import { getGroupTreeList, getStoreList, getUserInfoList } from '@/http/apis/center-control';
import { getErrorReportList, getRouteIdsByBatchDate, getWaitErrorReportList } from '@/http/apis/task-center';
import { formatOrganizationTreeToOptions, formatShopsToOptions, formatUserListToRoleOptions } from '@/utils/format';

export enum RequestName {
  GetGroupTreeList,
  GetStoreList,
  GetUserInfoList,
  GetRouteIdsByBatchDate,
  GetWaitErrorReportList,
  GetErrorReportList,
}

export const initState = {};

const Service = () => {
  const [service, executeRequest, dispatch] = useServiceReducer(initState, {
    [RequestName.GetGroupTreeList]: {
      request: HttpAop(getGroupTreeList, { after: [formatOrganizationTreeToOptions] }),
      afterRequest: (data, dispatch) => {
        dispatch({ organizationOptions: data });
      },
    },
    [RequestName.GetStoreList]: {
      request: HttpAop(getStoreList, {
        after: [formatShopsToOptions],
      }),
      afterRequest: (data, dispatch) => {
        dispatch({ storeOptions: data });
      },
    },
    [RequestName.GetUserInfoList]: {
      beforeRequest: (dispatch) => {
        dispatch({ userLoading: true });
      },
      request: HttpAop(getUserInfoList, {
        after: [(v) => formatUserListToRoleOptions(v, true)],
      }),
      afterRequest: (data, dispatch) => {
        dispatch({ userOptions: data, userLoading: false });
      },
    },
    [RequestName.GetRouteIdsByBatchDate]: {
      request: getRouteIdsByBatchDate,
      afterRequest: (data, dispatch) => {
        dispatch({
          routeIds: data?.map(({ id, batchId }) => {
            return {
              label: batchId,
              value: id,
            };
          }),
        });
      },
    },
    [RequestName.GetWaitErrorReportList]: {
      request: getWaitErrorReportList,
    },
    [RequestName.GetErrorReportList]: {
      request: getErrorReportList,
    },
  });

  return [service, executeRequest, dispatch];
};

export default Service;
