import AMapLoader from '@amap/amap-jsapi-loader';
import end from '@/assets/images/end.svg';
import MapAuditIcon from '@/assets/images/map-audited.svg';
import MapNotAuditIcon from '@/assets/images/map-no-audited.svg';
import midIcon from '@/assets/images/mid.svg';
import start from '@/assets/images/start.svg';

class MapRouteLine {
  private map: any = null;
  private AMap: any = null;
  // 当前路径规划
  private currentDriving: any = null;
  // 时间
  private time: string = '';
  // 距离
  private distance: string = '';
  // 原始定位数据
  private position: any = null;

  constructor(position) {
    // @ts-ignore
    window._AMapSecurityConfig = {
      securityJsCode: '180d07795656a13eaa0059339e4280be',
    };
    this.position = position;
  }

  /**
   * 初始化地图
   */
  async initMap(containerId: string) {
    try {
      this.AMap = await AMapLoader.load({
        key: '3691480a128250c7c159e46bea3db8a1',
        version: '2.0',
        plugins: [
          'AMap.ToolBar', // 缩放按钮
          'AMap.Scale', // 比例尺
          'AMap.Driving', // 驾车
        ],
      });

      this.map = new this.AMap.Map(containerId, {
        resizeEnable: true,
        zoom: 13,
        viewMode: '3D',
        center: [105.602725, 37.076636],
      });

      this.map.addControl(new this.AMap.ToolBar());
      this.map.addControl(new this.AMap.Scale());
    } catch (error) {
      console.error('地图初始化失败:', error);

      throw error;
    }
  }

  /**
   * 路径规划
   */
  async routePlanning(routes: any[]): Promise<void> {
    return new Promise((resolve, reject) => {
      const drivingOption = {
        policy: this.AMap.DrivingPolicy.LEAST_TIME, // 其它policy参数请参考 https://lbs.amap.com/api/javascript-api/reference/route-search#m_DrivingPolicy
      };

      this.currentDriving = new this.AMap.Driving(drivingOption);

      const routePoints = routes.map((item) => [item.longitude, item.latitude]);

      const start = routePoints?.[0];
      const end = routePoints?.[routePoints.length - 1];
      const waypoints = routePoints?.slice(1, routePoints.length - 1);

      this.currentDriving.search(start, end, { waypoints }, (status: any, result: any) => {
        if (status === 'complete') {
          if (result.routes && result.routes.length) {
            // 绘制第一条路线，也可以按需求绘制其它几条路线
            this.customRouteLine(result.routes[0], routes);
            this.distance = (result.routes[0].distance / 1000).toFixed(1); // m -> km
            this.time = (result.routes[0].time / 60).toFixed(); // second -> min
            resolve(); // 路径规划完成
          } else {
            reject(new Error('没有找到路径'));
          }
        } else {
          reject(new Error(`路径规划失败: ${status}`));
        }
      });
    });
  }

  parseRouteToPath(route) {
    const path = [];

    for (let i = 0, l = route.steps.length; i < l; i++) {
      const step = route.steps[i];

      for (let j = 0, n = step.path.length; j < n; j++) {
        path.push(step.path[j]);
      }
    }

    return path;
  }

  /**
   * 自定义绘画路线
   */
  async customRouteLine(route: any, routes: any) {
    const path = this.parseRouteToPath(route);

    const startStatus = ['COMPLETED', 'AUDITING'].includes(routes[0].taskStatus);
    const endStatus = ['COMPLETED', 'AUDITING'].includes(routes[routes.length - 1].taskStatus);

    const startMarker = new this.AMap.Marker({
      position: path[0],
      icon: new this.AMap.Icon({
        size: new this.AMap.Size(140, 30),
        image: start,
        imageSize: new this.AMap.Size(24, 36),
      }),
      offset: new this.AMap.Pixel(-70, -90),
      map: this.map,
      content: `<div class="flex flex-col items-center">
        <div style=" text-shadow:
        -1px -1px 0 white,
        1px -1px 0 white,
        -1px 1px 0 white,
        1px 1px 0 white;" class="text-sm font-medium text-[#1D2129]">${routes[0].shopName}</div>
        <img src="${start}" alt="start" style="height:48px;width:32px;object-fit: contain;" />
        <img src="${startStatus ? MapAuditIcon : MapNotAuditIcon}" alt="start" style="height:48px;width:32px;" />
      </div>`,
    });

    startMarker.on('click', () => {
      console.log('💀 ~ 点击起点 Marker:', routes[0]);
    });

    // TODO：途径节点
    routes?.slice(1, routes.length - 1)?.forEach((item) => {
      const marker = new this.AMap.Marker({
        position: new this.AMap.LngLat(item.longitude, item.latitude),
        // 将一张图片的地址设置为 icon
        icon: new this.AMap.Icon({
          size: new this.AMap.Size(140, 30),
          imageSize: new this.AMap.Size(24, 32),
        }),
        // 设置了 icon 以后，设置 icon 的偏移量，以 icon 的 [center bottom] 为原点
        offset: new this.AMap.Pixel(-70, -32),
        map: this.map,
        content: `<div class="flex flex-col items-center">
          <div style=" text-shadow:
        -1px -1px 0 white,
        1px -1px 0 white,
        -1px 1px 0 white,
        1px 1px 0 white;" class="text-xs font-medium text-[#1D2129]">${item.shopName}</div>
          <img src="${['COMPLETED', 'AUDITING'].includes(item.taskStatus) ? MapAuditIcon : MapNotAuditIcon}" alt="start" style="height:32px;width:24px" />
        </div>`,
      });

      marker.on('click', () => {
        console.log('💀 ~ 点击途径 Marker:', item);
      });
    });

    const endMarker = new this.AMap.Marker({
      position: path[path.length - 1],
      icon: new this.AMap.Icon({
        size: new this.AMap.Size(140, 30),
        image: end,
        imageSize: new this.AMap.Size(72, 90),
      }),
      offset: new this.AMap.Pixel(-70, -43),
      map: this.map,
      content: `<div class="flex flex-col items-center">
        <div style=" text-shadow:
        -1px -1px 0 white,
        1px -1px 0 white,
        -1px 1px 0 white,
        1px 1px 0 white;" class="text-sm font-medium text-[#1D2129]">${routes[routes.length - 1].shopName}</div>
        <img src="${end}" alt="start" style="height:48px;width:32px"/>
        <img src="${endStatus ? MapAuditIcon : MapNotAuditIcon}" alt="audit" style="height:48px;width:32px" />
      </div>`,
    });

    endMarker.on('click', () => {
      console.log('💀 ~ 点击终点 Marker:', routes[routes?.length - 1]);
    });

    const routeLine = new this.AMap.Polyline({
      path,
      bubble: true,
      isOutline: true,
      outlineColor: '#23C343',
      borderWeight: 1,
      strokeWeight: 6,
      strokeOpacity: 0.5,
      strokeColor: '#7BE188',
      lineJoin: 'round',
      showDir: true,
    });

    routeLine.setMap(this.map);

    // 调整视野达到最佳显示区域
    this.map.setFitView([startMarker, endMarker, routeLine]);
  }

  /**
   * 绘制新增门店的Marker
   */
  drawNewShopMarker(newShops: any, icon: string = midIcon) {
    // 遍历新增门店坐标点，为每个点创建 Marker
    newShops.forEach((item) => {
      // 创建新增门店的 Marker
      const newShopMarker = new this.AMap.Marker({
        position: new this.AMap.LngLat(item?.longitude, item?.latitude),
        icon: new this.AMap.Icon({
          size: new this.AMap.Size(140, 30),
          imageSize: new this.AMap.Size(24, 32),
        }),
        offset: new this.AMap.Pixel(-70, -32),
        map: this.map,
        content: `<div class="flex flex-col items-center">
          <div style="text-shadow:
            -1px -1px 0 white,
            1px -1px 0 white,
            -1px 1px 0 white,
            1px 1px 0 white;" class="text-xs font-medium text-[#1D2129]">${item?.shopName}</div>
          <img src="${icon}" alt="new-shop" style="height:32px;width:24px" />
        </div>`,
      });

      // 添加点击事件
      newShopMarker.on('click', () => {
        console.log('💀 ~ 点击drawNewShopMarker:', item);
        // 这里可以添加更多的点击处理逻辑，比如显示门店详情等
      });
    });
  }

  /**
   * 获取地图实例
   */
  getMap() {
    return this.map;
  }

  /**
   * 获取AMap实例
   */
  getAMap() {
    return this.AMap;
  }

  /**
   * 获取距离
   */
  getDistance() {
    return this.distance;
  }

  /**
   * 获取耗时
   */
  getTime() {
    return this.time;
  }

  /**
   * 销毁地图
   */
  destroy() {
    if (this.map) {
      this.map.destroy();
      this.map = null;
    }
  }
}

export default MapRouteLine;
