/* eslint-disable max-lines-per-function */
import { useEffect, useMemo, useRef, useState } from 'react';
import { Divider, Drawer, Tag, Timeline } from 'antd';
import dayjs from 'dayjs';
import { isNil } from 'lodash';
import MapRouteLine from './RouteLine';
import addIcon from '@/assets/images/add_icon.svg';
import auditIcon from '@/assets/images/audited.svg';
import changePersonIcon from '@/assets/images/change_person.svg';
import changeShop from '@/assets/images/change_shop.svg';
import dropShop from '@/assets/images/drop_shop.svg';
import locationIcon from '@/assets/images/location.svg';
import midIcon from '@/assets/images/mid.svg';
import notAuditIcon from '@/assets/images/not-audited.svg';
import { cn } from '@/utils/cn';

export interface AuditRoute {
  /**
   * 批次ID
   */
  batchId?: null | string;
  /**
   * 批次开始时间
   */
  beginTime?: string;
  /**
   * 批次结束时间
   */
  endTime?: string;
  /**
   * 预估执行时长，单位分钟
   */
  estimateExecuteTime?: number | null;
  /**
   * 线路ID
   */
  routeId?: number | null;
  /**
   * 门店信息列表
   */
  shopInfoDTOS?: FoodSafetyNormalRoutShopInfoDTO[] | null;
}

export interface FoodSafetyNormalRoutShopInfoDTO {
  /**
   * 稽核时间 单位：分钟
   */
  auditTime?: number | null;
  /**
   * 纬度
   */
  latitude?: null | number;
  /**
   * 经度
   */
  longitude?: null | number;
  /**
   * 门店地址
   */
  shopAddress?: null | string;
  /**
   * 门店ID
   */
  shopId?: null | string;
  /**
   * 门店名称
   */
  shopName?: null | string;
  /**
   * 排序
   */
  sort?: number | null;
  /**
   * 任务ID
   */
  taskId?: number | null;
  /**
   * 任务状态
   */
  taskStatus?: any;
  /**
   * 到达该门店时间 单位：分钟
   */
  travelTime?: number | null;
  /**
   * 是否新增门店
   */
  isBuffer?: number | null;
  /**
   * 是否转派的
   */
  isTransfer?: boolean | null;
}

interface MapRouteProps {
  open: boolean;
  onClose: () => void;
  data: AuditRoute;
}

export default function MapRoute({ open, onClose, data }: MapRouteProps) {
  const [activeKey, setActiveKey] = useState<number | null>(null);
  const mapInstance = useRef<MapRouteLine | null>();

  const [distance, setDistance] = useState('-');

  /**
   * 位置信息
   */
  const position = useMemo(() => {
    const canceledShopsDTOS: FoodSafetyNormalRoutShopInfoDTO[] = [];
    // 路径点
    const routePointsDTOS: FoodSafetyNormalRoutShopInfoDTO[] = [];
    // 新增门店
    const newShopPointsDTOS: FoodSafetyNormalRoutShopInfoDTO[] = [];
    // 转派门店
    const transferShopPointsDTOS: FoodSafetyNormalRoutShopInfoDTO[] = [];

    data?.shopInfoDTOS?.forEach((element) => {
      if (['CANCELED'].includes(element.taskStatus)) {
        canceledShopsDTOS.push(element);

        return;
      }

      if (element?.isTransfer) {
        transferShopPointsDTOS.push(element);

        return;
      }

      if (!!element?.isBuffer) {
        newShopPointsDTOS.push(element);

        return;
      }

      routePointsDTOS.push(element);
    });

    return {
      canceledShopsDTOS,
      routePointsDTOS,
      newShopPointsDTOS,
      transferShopPointsDTOS,
    };
  }, [data]);

  /**
   * 已查门店
   */
  const auditShopCount = useMemo(() => {
    return data?.shopInfoDTOS?.filter((item) => ['COMPLETED', 'AUDITING'].includes(item.taskStatus))?.length || 0;
  }, [data]);

  /**
   * 全部门店 要过滤掉作废门店
   */
  const allShopCount = useMemo(() => {
    return data?.shopInfoDTOS?.length - position.canceledShopsDTOS.length;
  }, [data?.shopInfoDTOS?.length, position.canceledShopsDTOS.length]);

  useEffect(() => {
    const initializeMap = async () => {
      try {
        mapInstance.current = new MapRouteLine(data?.shopInfoDTOS);

        await mapInstance.current.initMap('map_container_unique');

        // 现在可以安全地调用路径规划了
        await mapInstance.current.routePlanning(position?.routePointsDTOS);
        setDistance(mapInstance.current.getDistance());
        // 新增门店
        mapInstance.current.drawNewShopMarker(position.newShopPointsDTOS, midIcon);
        // 转派门店
        mapInstance.current.drawNewShopMarker(position.transferShopPointsDTOS, changeShop);
      } catch (error) {
        console.error('地图初始化失败:', error);
      }
    };

    if (
      open &&
      (position?.routePointsDTOS?.length ||
        position?.newShopPointsDTOS?.length ||
        position?.transferShopPointsDTOS?.length)
    ) {
      // 初始化地图
      initializeMap();
    }

    // 组件卸载时清理
    return () => {
      if (mapInstance.current) {
        // eslint-disable-next-line react-hooks/exhaustive-deps
        mapInstance?.current?.destroy();
        setActiveKey(null);
      }
    };
  }, [
    open,
    data?.shopInfoDTOS,
    position.newShopPointsDTOS,
    position?.routePointsDTOS,
    position.transferShopPointsDTOS,
  ]);

  const timelineItems = useMemo(() => {
    return position?.routePointsDTOS?.map((item, index) => ({
      dot: (
        <div
          className={cn(
            'w-6 h-6 bg-[#C9CDD4] rounded-full text-white flex items-center justify-center text-base font-medium',
            {
              'bg-[#23C343]': ['COMPLETED', 'AUDITING'].includes(item.taskStatus),
            },
            {
              'bg-white text-[#23C343] border border-solid border-[#23C343]': ['RUNNING', 'EXPIRED_RUNNING'].includes(
                item.taskStatus,
              ),
            },
            {
              'bg-black text-white': ['EXPIRED'].includes(item.taskStatus),
            },
          )}
        >
          {index + 1}
        </div>
      ),
      children: (
        <div
          style={{
            borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
          }}
          className="flex flex-col pb-4"
          onClick={() => {
            // 用taskId做key
            setActiveKey(item?.taskId);
            mapInstance.current.getMap().setCenter([item.longitude, item.latitude]);
            // 放大
            mapInstance.current.getMap().setZoom(15);
          }}
        >
          <div className="flex flex-row items-center justify-between">
            <div className="flex flex-row items-center">
              {index === 0 && (
                <Tag bordered color="success">
                  起点
                </Tag>
              )}
              {index === position?.routePointsDTOS.length - 1 && (
                <Tag bordered color="error">
                  终点
                </Tag>
              )}
              <div className="text-sm font-medium text-[#1D2129]">{item.shopName}</div>
            </div>
            <img src={locationIcon} alt="location" className="w-5 h-5" />
          </div>
          {item.shopAddress && <div className="text-xs font-normal text-[#86909C] mt-1">{item.shopAddress}</div>}
        </div>
      ),
    }));
  }, [position?.routePointsDTOS]);

  const days = useMemo(() => {
    const diffHours = dayjs(data?.endTime).diff(dayjs(data?.beginTime), 'hour');

    const _days = Math.floor(diffHours / 24);
    const hours = diffHours % 24;

    return `${_days}天${hours}小时`;
  }, [data?.beginTime, data?.endTime]);

  const activeShopInfo = useMemo(() => {
    return data?.shopInfoDTOS?.find((s) => {
      return s?.taskId === activeKey;
    });
  }, [activeKey, data?.shopInfoDTOS]);

  const renderExtraShopsLine = ({
    label,
    shops,
    icon,
    disable,
  }: {
    label: string;
    shops: FoodSafetyNormalRoutShopInfoDTO[];
    icon?: React.ReactNode;
    disable?: boolean;
  }) => {
    if (shops?.length) {
      return (
        <div
          className="flex gap-x-3 w-full "
          style={{
            transform: 'translatex(-6px)',
          }}
        >
          <div className="size-6">{icon}</div>
          <div className="flex-1 flex flex-col gap-y-4">
            <span className="text-sm leading-[22px] font-medium">{label}</span>
            {shops?.map((item) => (
              <div
                style={{
                  borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
                }}
                className="flex flex-col pb-4"
                onClick={() => {
                  if (disable) {
                    return;
                  }

                  // 用taskId做key
                  setActiveKey(item?.taskId);
                  mapInstance.current.getMap().setCenter([item.longitude, item.latitude]);
                  // 放大
                  mapInstance.current.getMap().setZoom(15);
                }}
              >
                <div className="flex flex-row items-center justify-between">
                  <div className="flex flex-row items-center">
                    <div className="text-sm font-medium text-[#1D2129]">{item.shopName}</div>
                  </div>
                  {!disable && <img src={locationIcon} alt="location" className="w-5 h-5" />}
                </div>
                {item.shopAddress && <div className="text-xs font-normal text-[#86909C] mt-1">{item.shopAddress}</div>}
              </div>
            ))}
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <Drawer width={640} title="路径规划" open={open} onClose={onClose}>
      <div className="bg-[#F7F8FA] p-3 rounded-lg">
        <div className="text-[#A7302A] text-bas font-medium">
          已查门店：{auditShopCount}/{allShopCount}
        </div>
        <div className="flex flex-row items-center mt-1">
          <div className="text-[#4E5969] text-sm font-normal">
            途经点：
            {position?.routePointsDTOS?.length - 2 < 0 ? 0 : position?.routePointsDTOS?.length - 2}
            个门店
          </div>
          <Divider type="vertical" />
          <div className="text-[#4E5969] text-sm font-normal">总行程：{distance}公里</div>
        </div>
        <div className="text-sm font-normal text-[#4E5969]">
          执行时间：{dayjs(data?.beginTime).format('YYYY-MM-DD HH:mm')} -{' '}
          {dayjs(data?.endTime).format('YYYY-MM-DD HH:mm')}（预估共{days}）
        </div>
      </div>
      <Timeline mode="left" className="mt-6" items={timelineItems} />
      <div className="flex flex-col gap-y-6 mb-8 empty:hidden">
        {/* 额外增加的门店 */}
        {renderExtraShopsLine({
          label: '额外增加门店',
          shops: position?.newShopPointsDTOS,
          icon: <img src={addIcon} alt="addIcon" className="w-6 h-6" />,
        })}
        {/* 转派门店 */}
        {renderExtraShopsLine({
          label: '转派门店',
          shops: position?.transferShopPointsDTOS,
          icon: <img src={changePersonIcon} alt="changePersonIcon" className="w-6 h-6" />,
        })}
        {/* 已作废的门店 */}
        {renderExtraShopsLine({
          label: '已作废的门店',
          shops: position?.canceledShopsDTOS,
          icon: <img src={dropShop} alt="dropShop" className="w-6 h-6" />,
          disable: true,
        })}
      </div>
      <div style={{ height: '460px' }} className="relative">
        <div id="map_container_unique" style={{ height: '460px' }} />
        <div className=" bg-[#1D2129] opacity-70 z-[999] absolute bottom-[8px] left-[8px] p-2 rounded flex flex-row">
          <div className="flex flex-col">
            <span className="text-white font-medium text-xs">门店类型说明</span>
            <div className="flex flex-row items-center mt-2">
              <img src={notAuditIcon} alt="location" className="w-5 h-5" />
              <span className="text-white font-medium text-xs ml-2">未稽核门店</span>
            </div>
            <div className="flex flex-row items-center mt-2">
              <img src={auditIcon} alt="location" className="w-5 h-5" />
              <span className="text-white font-medium text-xs ml-2">已稽核门店</span>
            </div>
            <div className="flex flex-row items-center mt-2">
              <img src={midIcon} alt="location" className="w-5 h-5" />
              <span className="text-white font-medium text-xs ml-2">新增门店</span>
            </div>
            <div className="flex flex-row items-center mt-2">
              <img src={changeShop} alt="location" className="w-5 h-5" />
              <span className="text-white font-medium text-xs ml-2">转派门店</span>
            </div>
          </div>
          {!isNil(activeKey) && (
            <>
              <div className="w-[1px] bg-white opacity-10 ml-3 mr-2" />
              <div className="flex flex-col justify-center">
                <div className="text-white font-medium text-xs">门店编号：{activeShopInfo?.shopId}</div>
                <div className="text-white font-medium text-xs mt-2">门店名称：{activeShopInfo?.shopName}</div>
                <div className="text-white font-medium text-xs mt-2">
                  坐标：{activeShopInfo?.longitude},{activeShopInfo?.latitude}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </Drawer>
  );
}
