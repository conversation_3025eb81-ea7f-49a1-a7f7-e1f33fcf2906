import { useEffect, useState } from 'react';
import { ProTable } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { Empty, Spin, Tabs } from 'antd';
import dayjs from 'dayjs';
import { PlanStatusEnumCN } from '../const';
import MediaCard from '@/components/MediaCard';
import { getExecutionDetails, getRouteOperateRecord } from '@/http/apis/task-center';
import createModal from '@/utils/antd/createModal';

interface DescModalProps {
  batchId: string;
  id: number;
}

function OperateRecord({ dataSource, loading }) {
  if (!dataSource?.length && !loading) {
    return <Empty className="my-6" />;
  }

  return (
    <Spin spinning={loading}>
      <div className="mb-6 max-h-[60vh] overflow-y-auto">
        {dataSource?.map((m, index) => {
          const remarkInfo = JSON.parse(m?.remark || '{}');

          return (
            <div className="mt-4" key={index}>
              <div className="flex justify-between">
                <div className="flex items-center gap-x-2">
                  <div className="size-8 rounded-full bg-blue-400 text-white flex justify-center items-center">
                    {m?.operateUserName?.slice(0, 1)}
                  </div>
                  <div className="flex gap-x-3">
                    <span>{m?.operateUserName}</span>
                    <span>修改路线计划信息</span>
                  </div>
                </div>
                {m?.createTime ? `${dayjs(m.createTime).format('YYYY-MM-DD HH:mm:ss')}` : '-'}
              </div>
              <div className="bg-gray-100 p-4 ml-10 mt-3">
                <div>修改原因：{remarkInfo?.reason}</div>
                <div>
                  稽核人员：由{remarkInfo?.beforeProcessUserName} 修改为 {remarkInfo?.afterProcessUserName}
                </div>
                {!!m?.attachmentIds?.length && (
                  <div className="mt-4 flex gap-3 flex-wrap">
                    {m.attachmentIds?.map((item) => (
                      <MediaCard file={item} key={item.id} fileList={m.attachmentIds} width={80} height={80} />
                    ))}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </Spin>
  );
}

enum TabsEnum {
  执行详情 = 'detail',
  操作记录 = 'record',
}

export const DescModal = createModal<{}, DescModalProps>(
  (_p, setVisible, payload) => {
    const { batchId, id } = payload?.data || {};

    const [activeKey, setActiveKey] = useState<string>(TabsEnum.执行详情);

    const { data, loading } = useRequest(() => getExecutionDetails(batchId), {
      refreshDeps: [batchId],
      ready: !!batchId && !!_p.visible && activeKey === TabsEnum.执行详情,
    });

    const { data: recordData, loading: recordLoading } = useRequest(
      async () => {
        const res = await getRouteOperateRecord(id);

        return res;
      },
      {
        refreshDeps: [id],
        ready: !!id && !!_p.visible && activeKey === TabsEnum.操作记录,
      },
    );

    const columns = [
      {
        title: '计划稽核门店数',
        dataIndex: 'plannedAuditShopCount',
      },
      {
        title: '计划状态',
        dataIndex: 'routeBatchStatus',
        valueEnum: PlanStatusEnumCN,
        width: 120,
      },
      {
        title: '进行中门店数',
        dataIndex: 'taskRunningCount',
      },
      {
        title: '已稽核门店数',
        dataIndex: 'completedCount',
      },
      {
        title: '逾期执行门店数',
        dataIndex: 'expiredRunningCount',
      },
      {
        title: '已过期门店数',
        dataIndex: 'expiredCount',
      },
      // {
      //   title: '实际执行时长',
      //   dataIndex: 'actualDuration',
      //   render: (_, record) => {
      //     return record?.actualDuration ? `${record?.actualDuration}小时` : '-';
      //   },
      // },
    ];

    useEffect(() => {
      if (_p.visible) {
        setActiveKey(TabsEnum.执行详情);
      }
    }, [_p.visible]);

    const tabs = [
      {
        key: TabsEnum.执行详情,
        label: '执行详情',
        children: (
          <ProTable
            loading={loading}
            columns={columns}
            search={false}
            options={false}
            dataSource={data ? [data] : []}
            pagination={false}
          />
        ),
      },
      {
        key: TabsEnum.操作记录,
        label: '操作记录',
        children: <OperateRecord dataSource={recordData} loading={recordLoading} />,
      },
    ];

    return [
      {
        footer: null,
      },
      <Tabs items={tabs} activeKey={activeKey} onChange={setActiveKey} />,
    ];
  },
  {
    title: '详情',
    width: 856,
  },
);
