/* eslint-disable max-lines-per-function */
import { Key, useEffect, useRef, useState } from 'react';
import { ActionType, ProCard, ProForm, ProTable } from '@ant-design/pro-components';
import { But<PERSON>, Space } from 'antd';
import { type DefaultOptionType } from 'antd/lib/select';
import dayjs from 'dayjs';
import { isEmpty, isNil } from 'lodash';
import { CheckRouteButton } from './CheckRouteButton';
import { ModifyBatchModalForm } from './ModifyBatchModalForm';
import { OprModal } from './OprModal';
import useTaskDetailModal from '../../list/detail/hooks/use-task-detail';
import { descStatusEnum, descStatusEnumCN } from '../const';
import Service, { RequestName } from '../service';
import { Permission } from '@/components/Permission';
import { getColumns, getCommonConfig, getPersonColumn, getStoreColumn } from '@/components/pro-table-config';
import { Auth } from '@/constants/auth';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import useUserListCaching from '@/hooks/use-user-list-caching';
import { parserParams } from '@/utils/convert';
import { formatDateToUTC } from '@/utils/date';
import eventEmitter from '@/utils/eventEmitter';
import { PaginationParams } from '@/utils/pagination';

// 处理禁用选项
function disabledOption(item: DefaultOptionType, taskStatus: Array<string | number>) {
  if (taskStatus && Array.isArray(taskStatus)) {
    // 是否有逾期已完成
    const hasExpiredCompleted = taskStatus.includes(-1);

    return {
      ...item,
      // 逾期已完成只能单独选择，其他任务状态下不可选
      disabled: item?.value === -1 ? taskStatus.length && !hasExpiredCompleted : hasExpiredCompleted,
    };
  } else {
    return item;
  }
}

export const Detail = () => {
  const [urlParams, setUrlParams] = useQuerySearchParams();
  const _detailUrlParams = parserParams(urlParams?.detail || {});

  const [paginationParams, setPaginationParams] = useState<PaginationParams>({ pageNo: 1, pageSize: 10 });
  const [form] = ProForm.useForm();
  const actionRef = useRef<ActionType>();
  const [selectKeys, setSelectKeys] = useState<Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const { showModal } = useTaskDetailModal();

  const [service, executeRequest] = Service();

  const { userListCachingRef } = useUserListCaching({ options: service?.reviewerOptions });

  const getRouteIdsByBatchDate = (date?: string) => {
    executeRequest(RequestName.GetRouteIdsByBatchDate, date);
  };

  const taskStatus = ProForm.useWatch('taskStatus', form);

  const columns = getColumns({
    searchColumns: [
      getStoreColumn({
        organizationOptions: service?.organizationOptions,
        storeOptions: service?.storeOptions,
        onStoreFocus: (groupId: any) => {
          executeRequest(RequestName.GetStoreList, {
            fightGroupId: groupId,
            groupType: 2,
            privilegeCode: 1,
          });
        },
      }),
      getPersonColumn(
        {
          options: userListCachingRef?.current || service?.reviewerOptions,
          onFocus: () => {
            if (!userListCachingRef?.current) {
              executeRequest(RequestName.GetUserInfoList);
            }
          },
          loading: service?.reviewerLoading,
        },
        {
          dataIndex: 'processUserIds',
          title: '任务执行人',
          colSize: 1.5,
          search: {
            transform: (value: any) => {
              return {
                processUserIds: Array.isArray(value) ? value : [value],
              };
            },
          },
          fieldProps: { mode: 'multiple' },
        },
      ),
      {
        title: '人工是否有调整',
        dataIndex: 'isModify',
        valueType: 'select',
        valueEnum: {
          true: '是',
          false: '否',
        },
        colSize: 1.5,
      },
      {
        title: '路线ID',
        dataIndex: 'foodSafetyNormalRouteBatchId',
        valueType: 'select',
        fieldProps: {
          options: service?.routeIds,
          showSearch: true,
        },
        colSize: 1.5,
      },
      {
        title: '任务状态',
        dataIndex: 'taskStatus',
        valueType: 'select',
        colSize: 1.5,
        search: {
          transform: (value: any) => {
            return {
              taskStatus: Array.isArray(value) ? value : [value],
            };
          },
        },
        fieldProps: {
          mode: 'multiple',
          options: [
            { label: '待开始', value: descStatusEnum.WAITING_START },
            { label: '待确认', value: descStatusEnum.AUDITING },
            { label: '进行中', value: descStatusEnum.RUNNING },
            { label: '已完成', value: descStatusEnum.COMPLETED },
            { label: '已作废', value: descStatusEnum.CANCELED },
            { label: '已逾期', value: descStatusEnum.EXPIRED },
            { label: '逾期进行中', value: descStatusEnum.EXPIRED_RUNNING },
            // 后端无这个状态, 需要前端特殊处理
            { label: '逾期已完成', value: -1 },
          ]?.map((m) => disabledOption(m, taskStatus)),
        },
      },
      {
        title: '任务提交时间',
        key: 'submitTime',
        valueType: 'dateRange',
        search: {
          transform: (value: any) => {
            return {
              taskSubmitBeginTime: formatDateToUTC(dayjs(value[0]).startOf('day')),
              taskSubmitEndTime: formatDateToUTC(dayjs(value[1]).endOf('day')),
            };
          },
        },
        colSize: 2,
      },
      {
        title: '稽核月份',
        dataIndex: 'batchDate',
        valueType: 'dateMonth',
        colSize: 1.5,
        initialValue: dayjs().format('YYYY-MM'),
        fieldProps: {
          allowClear: false,
          onChange: (_, dateString) => {
            getRouteIdsByBatchDate(dateString);
          },
        },
      },
    ],
    tableColumns: [
      {
        title: '路线ID',
        dataIndex: 'batchId',
        width: 300,
      },
      {
        title: '门店id',
        dataIndex: 'shopId',
        width: 150,
      },
      {
        title: '门店名称',
        dataIndex: 'shopName',
        width: 250,
      },
      {
        title: '任务执行人',
        dataIndex: 'processUserName',
        width: 200,
      },
      {
        title: '任务类型',
        key: 'taskSubType',
        width: 200,
        render: () => {
          return '食安线下稽核';
        },
      },
      {
        title: '路径预览',
        key: 'preview',
        render: (_, record) => {
          return <CheckRouteButton batchId={record?.batchId} />;
        },
        width: 150,
      },
      {
        title: '任务执行时段',
        key: 'time',
        width: 230,
        render: (_, record) => {
          return record?.taskBeginTime && record?.taskExpiredTime ? (
            <div className="flex flex-col shrink-0">
              <span>{dayjs(record?.taskBeginTime).format('YYYY-MM-DD HH:mm:ss')}</span>
              <span>{dayjs(record?.taskExpiredTime).format('YYYY-MM-DD HH:mm:ss')}</span>
            </div>
          ) : (
            '-'
          );
        },
      },
      {
        title: '任务状态',
        dataIndex: 'taskStatus',
        valueEnum: descStatusEnumCN,
        width: 150,
        render: (_, record) => {
          if (record?.taskStatus === descStatusEnum.COMPLETED && record?.hasExpired) {
            return '逾期已完成';
          }

          return descStatusEnumCN[record?.taskStatus];
        },
      },
      {
        title: '任务提交时间',
        dataIndex: 'taskSubmitTime',
        width: 230,
        render: (_, record) => {
          return record?.taskSubmitTime ? dayjs(record?.taskSubmitTime).format('YYYY-MM-DD HH:mm:ss') : '-';
        },
      },
      {
        title: '操作',
        valueType: 'option',
        fixed: 'right',
        render: (_, record) => {
          return (
            <Space>
              <Permission permission={Auth.任务中心_稽核排班管理_修改任务执行信息}>
                {/* 待开始 && taskId不为空 */}
                {record?.taskStatus === descStatusEnum.WAITING_START && record?.taskId && (
                  <Button
                    size="small"
                    type="link"
                    onClick={() => {
                      ModifyBatchModalForm.showModal({
                        data: {
                          isInfoModal: true,
                          ids: [record?.id],
                          processUserId: record?.processUserId,
                          batchDate: dayjs(form.getFieldValue('batchDate')).format('YYYY-MM'),
                          onSuccess: () => {
                            actionRef.current.reload();
                            setSelectKeys([]);
                            setSelectedRows([]);
                          },
                        },
                      });
                    }}
                  >
                    编辑
                  </Button>
                )}
              </Permission>
              {record?.isModify && (
                <Button
                  size="small"
                  type="link"
                  onClick={() => {
                    OprModal.showModal({
                      data: {
                        taskId: record?.taskId,
                      },
                    });
                  }}
                >
                  操作记录
                </Button>
              )}
            </Space>
          );
        },
      },
    ],
  });

  const formatFormValue = (value: any) => {
    const { store, taskStatus, ...rest } = value;

    const buildTaskStatus = () => {
      if (isNil(taskStatus)) {
        return {};
      }

      if (taskStatus?.[0] === -1) {
        return {
          taskStatus: [descStatusEnum.COMPLETED],
          hasExpired: true,
        };
      }

      return { taskStatus };
    };

    return { ...store, ...rest, ...buildTaskStatus() };
  };

  useEffect(() => {
    executeRequest(RequestName.GetGroupTreeList);
    getRouteIdsByBatchDate();

    if (!isEmpty(_detailUrlParams)) {
      const { store, isModify, taskSubmitBeginTime, taskSubmitEndTime, ...rest } = _detailUrlParams;

      const { groupId, shopIds } = store || {};

      if (_detailUrlParams?.processUserIds) {
        executeRequest(RequestName.GetUserInfoList);
      }

      if (shopIds?.length) {
        executeRequest(RequestName.GetStoreList, {
          fightGroupId: groupId,
          groupType: 2,
          privilegeCode: 1,
        });
      }

      form.setFieldsValue({
        ...rest,
        store,
        isModify: !isNil(isModify) ? isModify?.toString() : undefined,
        submitTime: taskSubmitBeginTime && taskSubmitEndTime ? [taskSubmitBeginTime, taskSubmitEndTime] : undefined,
        processUserIds: rest?.processUserIds
          ? Array.isArray(rest.processUserIds)
            ? rest?.processUserIds?.map((m) => +m)
            : [rest?.processUserIds]
          : undefined,
      });

      setPaginationParams({
        pageNo: _detailUrlParams?.pageNo || 1,
        pageSize: _detailUrlParams?.pageSize || 10,
      });
    }

    form.submit();

    eventEmitter.on('GET_SCHEDULINGDESC_WITH_BATCHID', ({ foodSafetyNormalRouteBatchId, batchDate }) => {
      form.resetFields();
      form.setFieldsValue({ foodSafetyNormalRouteBatchId, batchDate });

      form.submit();
    });

    return () => {
      eventEmitter.off('GET_SCHEDULINGDESC_WITH_BATCHID');
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <ProTable
        {...getCommonConfig({
          search: {
            form,
          },
        })}
        scroll={{ x: 2000 }}
        actionRef={actionRef}
        rowKey="id"
        options={false}
        columns={columns}
        pagination={{
          showSizeChanger: true,
          current: paginationParams.pageNo,
          pageSize: paginationParams.pageSize,
          onChange: (pageNo, pageSize) => {
            setPaginationParams({ pageNo, pageSize });
          },
        }}
        tableExtraRender={() => (
          <ProCard>
            <Permission permission={Auth.任务中心_稽核排班管理_修改任务执行信息}>
              <Button
                type="primary"
                disabled={!selectKeys?.length}
                onClick={() => {
                  ModifyBatchModalForm.showModal({
                    data: {
                      isInfoModal: true,
                      ids: selectKeys,
                      processUserIds: selectedRows?.length
                        ? Array.from(new Set(selectedRows?.map((m) => m?.processUserId)))
                        : [],
                      batchDate: dayjs(form.getFieldValue('batchDate')).format('YYYY-MM'),
                      onSuccess: () => {
                        actionRef.current.reload();
                        setSelectKeys([]);
                        setSelectedRows([]);
                      },
                    },
                  });
                }}
              >
                修改任务执行信息
              </Button>
            </Permission>
          </ProCard>
        )}
        tableRender={(_p, _d, { table }) => {
          return <ProCard>{table}</ProCard>;
        }}
        manualRequest={true}
        request={async (value) => {
          const { current, pageSize, ...val } = value;

          const payload = {
            ...formatFormValue(val),
            pageNo: current || _detailUrlParams?.current || 1,
            pageSize: pageSize || _detailUrlParams?.pageSize || 10,
          };

          const _urlParams = {
            ...val,
            pageNo: current || _detailUrlParams?.current || 1,
            pageSize: pageSize || _detailUrlParams?.pageSize || 10,
          };

          setUrlParams({
            ...urlParams,
            detail: _urlParams,
          });

          setPaginationParams({
            pageNo: current || _detailUrlParams?.current || 1,
            pageSize: pageSize || _detailUrlParams?.pageSize || 10,
          });

          const res = await executeRequest(RequestName.GetAuditRouteDataPageList, payload);

          return {
            data: res?.data || [],
            success: true,
            total: res?.total,
          };
        }}
        rowSelection={{
          selectedRowKeys: selectKeys,
          type: 'checkbox',
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectKeys(selectedRowKeys);
            setSelectedRows(selectedRows);
          },
          renderCell: (_, record, index, originNode) => {
            // 待开始或者无状态
            if (record?.taskStatus === descStatusEnum.WAITING_START && record?.taskId) {
              return originNode;
            }

            return null;
          },
          getCheckboxProps: (record) => ({
            disabled: !(record?.taskStatus === descStatusEnum.WAITING_START && record?.taskId),
          }),
        }}
      />
      <ModifyBatchModalForm />
      <OprModal />
    </>
  );
};
