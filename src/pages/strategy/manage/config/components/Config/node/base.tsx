import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import {
  ModalForm,
  ProForm,
  ProFormDependency,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadDragger,
} from '@ant-design/pro-components';
import { message } from 'antd';
import Synchrodata from './synchrodata';
import { NodeEnum } from '../../flow-chart/node/common';
import ShopAutoJoin from '../../ShopAutoJoin';
import { EdgeTriggerType } from '../edge/constants';
import { NodeConfigProps } from '.';
import ItemCard from '@/components/item-card';
import OrganizationCascader from '@/components/organization-cascader';
/* import OrganizationOrStoreSelectModal, {
  ModalType,
  OrganizationOrStoreSelectModalProps,
} from '@/components/organization-or-store-select-modal'; */
import RoleCascader from '@/components/role-cascader';
import {
  StrategyPatrolType,
  StrategyPatrolTypeCN,
  StrategyRoutineType,
  StrategyTaskType,
  StrategyTaskTypeCN,
  StrategyVideoPatrolType,
  StrategyVideoPatrolTypeCN,
  TaskSubType,
} from '@/constants/strategy';
import { useOSSClient } from '@/hooks/use-oss-client';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { getMessageWarningEnableList } from '@/http/apis/task-center';
import eventEmitter from '@/utils/eventEmitter';

// 根据shopids查询 门店组织树结构数据
/* function queryGroupShopTreeByShopIds(groupShopTreeOptions: any[], shopIds: string[]) {
  if (!groupShopTreeOptions?.length || !shopIds?.length) {
    return [];
  }

  const map = new Map<string, any>();

  const traverse = (nodes) => {
    nodes?.forEach((node) => {
      const shopId = node?.value?.split('-')?.[1];

      if (shopId) {
        map.set(shopId, node);
      }

      if (node.children?.length) {
        traverse(node.children);
      }
    });
  };

  traverse(groupShopTreeOptions);

  const result = [];

  shopIds?.forEach((id) => {
    if (map.has(id)) {
      result.push(map.get(id));
    }
  });

  return result;
} */

// eslint-disable-next-line max-lines-per-function
const BaseNode: FC<NodeConfigProps> = ({
  roleOptions,
  onTaskTypeChange,
  templateOptions,
  organizationOptions,
  // groupShopTreeOptions,
  shopOptions,
  transferOptions,
  differenceItemArriveShopTemplateOptions,
  cell,
  form,
  edges,
  downloadTemplate,
  submitExcel,
}) => {
  const [params] = useQuerySearchParams();

  const [batchModalProps, setBatchModal] = useState<{ open: boolean }>({
    open: false,
  });

  const [MessageWarningEnableList, setMessageWarningEnableList] = useState<any[]>([]);

  const [isDataFoodEvent, setIsDataFoodEvent] = useState(false);

  useEffect(() => {
    if (cell?.data?.taskType) {
      onTaskTypeChange?.({
        type: cell?.data?.taskType,
        taskSubType: cell?.data?.taskSubType,
      });
    }

    eventEmitter.on('triggerType', (data) => {
      console.log('接收到triggerType:', data);
      setTriggerType(data);
    });
    eventEmitter.on('is-DATA_FOOD_SAFETY_NORMAL-Event', (data) => {
      setIsDataFoodEvent(data);
    });

    return () => {
      eventEmitter.off('triggerType');
      eventEmitter.off('is-DATA_FOOD_SAFETY_NORMAL-Event');
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const [errorList, setErrorList] = useState([]);
  const [triggerType, setTriggerType] = useState('');
  const { uploadFile } = useCallback(useOSSClient, [])('TMP');
  const excels = ProForm?.useWatch('excels');
  const taskType = ProForm.useWatch('taskType', form);
  const taskSubType = ProForm.useWatch('taskSubType', form);
  const disableDifferenceItemArriveShopTemplate = useMemo(() => {
    let flag = false;

    edges?.forEach((edge) => {
      const sourceCell = edge.getSourceCell();

      // tips: 存在两个基础任务，则当第二个基础任务的任务类型为自检的时候，禁用审核条件模板
      if (
        sourceCell?.id !== cell?.id &&
        [NodeEnum.基础任务].includes(sourceCell?.getData()?.type) &&
        taskType === StrategyTaskType.SELF
      ) {
        form?.setFieldsValue({
          templateDifferenceAuditId: undefined,
        });
        flag = true;
      }
    });

    return flag;
  }, [edges, cell, taskType, form]);

  /* const [modalProps, setModalProps] = useState<{
    open: boolean;
    type: ModalType;
    value?: any;
    options?: OrganizationOrStoreSelectModalProps['options'];
  }>({
    open: false,
    type: ModalType.STORE,
    value: undefined,
  }); */

  useEffect(() => {
    if (taskType === TaskSubType.SELF) {
      form.setFieldValue('taskSubType', undefined);

      cell?.setData(
        {
          ...cell?.getData(),
          taskSubType: undefined,
        },
        { overwrite: true },
      );
    }
  }, [form, taskType, cell]);

  useEffect(() => {
    if (taskType === TaskSubType.PATROL) {
      // 断开回流的边
      edges?.forEach((edge) => {
        if (edge?.getSourceCell()?.id === edge?.getTargetCell()?.id) {
          edge?.remove();
        }
      });
    }
  }, [edges, taskType]);

  useEffect(() => {
    if (isDataFoodEvent) {
      // 只能是巡检任务-食安线下稽核
      onTaskTypeChange?.({ type: StrategyTaskType.PATROL, taskSubType: StrategyPatrolType.FOOD_SAFETY_NORMAL });

      form.setFieldValue('taskType', StrategyTaskType.PATROL);
      form.setFieldValue('taskSubType', StrategyRoutineType.FOOD_SAFETY_NORMAL);

      cell?.setData(
        {
          ...cell?.getData(),
          taskType: StrategyTaskType.PATROL,
          taskSubType: StrategyRoutineType.FOOD_SAFETY_NORMAL,
        },
        { overwrite: true },
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDataFoodEvent]);

  const getMessageOption = (taskType, taskSubType) => {
    setMessageWarningEnableList([]);

    form?.setFieldsValue({
      templateDifferenceAuditId: undefined,
    });
    cell?.setData(
      {
        ...cell?.getData(),
        templateDifferenceAuditId: undefined,
      },
      { overwrite: true },
    );

    getMessageWarningEnableList({ taskType, taskSubType }).then((res: any) => {
      setMessageWarningEnableList(
        () =>
          res?.map((m: any) => {
            return {
              label: m?.name,
              value: m?.id,
            };
          }) || [],
      );
    });
  };

  useEffect(() => {
    if (taskType) {
      if (taskType === TaskSubType.SELF && !taskSubType) {
        getMessageOption(taskType, 'SELF');

        return;
      }

      // 非自检 需要保证taskSubType不为空
      if (taskType !== TaskSubType.SELF && taskSubType) {
        getMessageOption(taskType, taskSubType);
      }
    }
  }, [taskType, taskSubType]);

  return (
    <>
      <ItemCard title="基础任务设置">
        <ProFormText
          label="节点名称"
          name="nodeName"
          rules={[
            {
              required: true,
              max: 30,
              message: '请输入最多30个字的节点名称,',
            },
          ]}
        />
        <ProFormSelect
          label="任务类型"
          name="taskType"
          rules={[
            {
              required: true,
              message: '请选择任务类型',
            },
          ]}
          fieldProps={{
            onChange: (val: string) => {
              onTaskTypeChange?.({ type: val });
              form?.setFieldsValue({
                templateId: undefined,
                taskShopFlag: false,
                excels: undefined,

                transferTemplateId: undefined,
                templateDifferenceAuditId: undefined,
              });

              /** 
              const data = cell?.getData();

              cell?.setData({
                ...data,
                taskShopFlag: false,
                excels: undefined,
              });
              */

              cell?.setData(
                {
                  ...cell?.getData(),
                  taskShopFlag: false,
                  excels: undefined,

                  templateId: undefined,
                  transferTemplateId: undefined,
                  templateDifferenceAuditId: undefined,
                },
                { overwrite: true },
              );
            },
          }}
          valueEnum={{
            [StrategyTaskType.SELF]: StrategyTaskTypeCN[StrategyTaskType.SELF],
            [StrategyTaskType.PATROL]: StrategyTaskTypeCN[StrategyTaskType.PATROL],
          }}
          disabled={isDataFoodEvent}
        />
        <ProFormDependency name={['taskType']}>
          {({ taskType }) => {
            return (
              taskType === StrategyTaskType.PATROL && (
                <ProFormSelect
                  label="巡检类型"
                  name="taskSubType"
                  rules={[
                    {
                      required: true,
                      message: '请选择巡检类型',
                    },
                  ]}
                  fieldProps={{
                    onChange: (val) => {
                      onTaskTypeChange?.({ type: taskType, taskSubType: val });
                      form?.setFieldsValue({
                        templateId: undefined,
                        videoPatrolType: undefined,
                        videoPatrolLimit: undefined,
                      });
                      cell?.setData({
                        ...cell?.getData(),
                        videoPatrolType: undefined,
                        videoPatrolLimit: undefined,
                      });
                    },
                  }}
                  // valueEnum={StrategyPatrolTypeCN}
                  options={Object.keys(StrategyPatrolTypeCN)
                    .filter((key) =>
                      params?.safetyFlag !== 'true'
                        ? ![StrategyPatrolType.FOOD_SAFETY_NORMAL, StrategyPatrolType.FOOD_SAFETY_VIDEO].includes(
                            key as StrategyPatrolType,
                          )
                        : true,
                    )
                    .map((key: string) => ({
                      label: StrategyPatrolTypeCN[key],
                      value: key,
                    }))}
                  disabled={isDataFoodEvent}
                />
              )
            );
          }}
        </ProFormDependency>
        <ProFormSelect
          label="任务选择"
          name="templateId"
          placeholder="通过任务模板名称或ID模糊搜索"
          rules={[
            {
              required: true,
              message: '请选择任务',
            },
          ]}
          fieldProps={{
            showSearch: true,
            filterOption: (inputValue, options) => {
              if (inputValue) {
                const { value, label }: any = options;

                if (label?.includes(inputValue) || value?.toString()?.includes(inputValue)) {
                  return true;
                }
              }

              return false;
            },
            onChange: () => {
              form.setFieldsValue({
                transferTemplateId: undefined,
                templateDifferenceAuditId: undefined,
              });

              cell?.setData(
                {
                  ...cell?.getData(),

                  transferTemplateId: undefined,
                  templateDifferenceAuditId: undefined,
                },
                { overwrite: true },
              );
            },
          }}
          options={templateOptions}
        />
        {/* <ProFormSelect
          label="处理人角色类型"
          name=""
          placeholder="请选择处理人角色类型"
          rules={[
            {
              required: true,
              message: '请选择处理人角色类型',
            },
          ]}
          fieldProps={{
            showSearch: true,
          }}
          // options={templateOptions}
        /> */}
        {triggerType !== EdgeTriggerType.EVENT &&
          taskType !== StrategyTaskType.SELF &&
          taskSubType !== StrategyPatrolType.HANDOVER && (
            <ProFormRadio.Group
              label="是否通过表格导入配置"
              name={'taskShopFlag'}
              initialValue={false}
              options={[
                {
                  label: '是',
                  value: true,
                },
                {
                  label: '否',
                  value: false,
                },
              ]}
            />
          )}

        <ProFormDependency name={['taskShopFlag', 'taskType', 'taskSubType']}>
          {({ taskShopFlag, taskType, taskSubType }) => {
            return taskShopFlag && triggerType !== EdgeTriggerType.EVENT ? (
              <div>
                <a
                  className="flex mb-4"
                  onClick={() => {
                    downloadTemplate?.();
                  }}
                >
                  下载模板
                </a>
                <ProFormUploadDragger
                  name="excels"
                  max={1}
                  fieldProps={{
                    customRequest: async (e: any) => {
                      try {
                        const response = await uploadFile(e.file, true, null, {});

                        const res: any = await submitExcel({
                          nodeUuid: cell?.id,
                          fileName: response?.originName,
                          url: response?.url,
                        });

                        if (!res || !res?.length) {
                          e.onSuccess(response);
                          setErrorList([]);
                        } else {
                          if (Array.isArray(res)) {
                            setErrorList(res);
                          }

                          e.onError();
                        }
                      } catch (error) {
                        e.onError();
                      } finally {
                      }
                    },
                  }}
                />
                {excels?.length && errorList?.length
                  ? errorList?.map((errMsg: string, index) => {
                      return (
                        <div key={index} className="text-[#ff4d4f] text-sm">
                          {errMsg}
                        </div>
                      );
                    })
                  : undefined}
              </div>
            ) : (
              <>
                {taskType === StrategyTaskType.PATROL &&
                ![StrategyPatrolType.HANDOVER].includes(taskSubType) &&
                !isDataFoodEvent ? (
                  <ProForm.Item
                    label="角色选择"
                    name="roleIds"
                    rules={[
                      {
                        required: true,
                        message: '请选择角色',
                      },
                    ]}
                  >
                    <RoleCascader options={roleOptions} multiple={true} />
                  </ProForm.Item>
                ) : null}
                <Synchrodata edges={edges} cell={cell} form={form} />
                {triggerType !== EdgeTriggerType.EVENT && taskSubType !== StrategyPatrolType.HANDOVER && (
                  <React.Fragment>
                    <ProForm.Item label="组织选择" name="groupIds">
                      <OrganizationCascader
                        placeholder="请选择组织"
                        options={organizationOptions}
                        multiple={true}
                        expandTrigger={'click'}
                        // onChange={(val) => {
                        //   onOrganizationChange?.(val);
                        // }}
                      />
                    </ProForm.Item>
                    {/* <OrganizationField name="groupIds" /> */}
                    <ProFormSelect
                      label="门店选择"
                      name="shopIds"
                      options={shopOptions}
                      fieldProps={{
                        mode: 'multiple',
                      }}
                      width={180}
                      addonAfter={
                        <a
                          onClick={() => {
                            setBatchModal({
                              open: true,
                            });
                          }}
                        >
                          批量导入
                        </a>
                      }
                    />
                    <ProFormDependency name={['taskSubType', 'videoPatrolType']}>
                      {({ taskSubType, videoPatrolType }) => {
                        return (
                          [StrategyRoutineType.VIDEO, StrategyRoutineType.FOOD_SAFETY_VIDEO].includes(taskSubType) && (
                            <>
                              <ProFormSelect
                                label="大数据门店结果"
                                name="videoPatrolType"
                                valueEnum={{
                                  [StrategyVideoPatrolType.ONLINE_SPECIALIST]:
                                    StrategyVideoPatrolTypeCN[StrategyVideoPatrolType.ONLINE_SPECIALIST],
                                  // [StrategyVideoPatrolType.ONLINE_SAFETY_VIDEO]:
                                  //   StrategyVideoPatrolTypeCN[StrategyVideoPatrolType.ONLINE_SAFETY_VIDEO],
                                }}
                                fieldProps={{
                                  onChange: (val) => {
                                    if (!val) {
                                      form.setFieldValue('videoPatrolLimit', undefined);

                                      cell?.setData({
                                        ...cell?.getData(),
                                        videoPatrolLimit: null,
                                      });
                                    }
                                  },
                                }}
                                // rules={[
                                //   {
                                //     required: true,
                                //     message: '请选择大数据门店结果',
                                //   },
                                // ]}
                              />
                              {videoPatrolType && (
                                <ProFormDigit
                                  label="每周巡检门店数量"
                                  name="videoPatrolLimit"
                                  fieldProps={{ precision: 0, min: 0, suffix: '个' }}
                                  width="xs"
                                  rules={[
                                    {
                                      required: true,
                                      message: '请输入每周巡检门店数量',
                                    },
                                  ]}
                                />
                              )}
                            </>
                          )
                        );
                      }}
                    </ProFormDependency>
                    <ProFormRadio.Group
                      label="包含筹备中和待营业的门店"
                      name="readyShopFlag"
                      required
                      initialValue={false}
                      options={[
                        {
                          label: '是',
                          value: true,
                        },
                        {
                          label: '否',
                          value: false,
                        },
                      ]}
                    />
                    {taskType === StrategyTaskType.SELF && <ShopAutoJoin form={form} />}
                  </React.Fragment>
                )}
              </>
            );
          }}
        </ProFormDependency>

        <ProForm.Item dependencies={['taskSubType', 'auditFlag']} noStyle>
          {({ getFieldValue }) => {
            const taskSubType = getFieldValue('taskSubType');
            const auditFlag = getFieldValue('auditFlag');

            return (
              taskSubType === 'DIAGNOSTIC' && (
                <>
                  <ProFormRadio.Group
                    label="任务下发审核"
                    name="auditFlag"
                    required
                    initialValue={false}
                    preserve={false}
                    options={[
                      {
                        label: '否',
                        value: false,
                      },
                      {
                        label: '是',
                        value: true,
                      },
                    ]}
                  />
                  {auditFlag && (
                    <ProForm.Item
                      label="角色选择"
                      name="auditRoles"
                      preserve={false}
                      rules={[
                        {
                          required: true,
                          message: '请选择角色',
                        },
                      ]}
                    >
                      <RoleCascader options={roleOptions} multiple />
                    </ProForm.Item>
                  )}
                </>
              )
            );
          }}
        </ProForm.Item>
      </ItemCard>
      {![StrategyPatrolType.HANDOVER].includes(taskSubType) && (
        <ItemCard title="任务操作条件">
          <ProFormDependency name={['taskSubType', 'taskType']}>
            {({ taskSubType, taskType }) => {
              return (
                <>
                  {!isDataFoodEvent && (
                    <ProFormSelect
                      label="转派条件模板"
                      disabled={params?.readonly || !taskType || !taskSubType}
                      name="transferTemplateId"
                      options={transferOptions}
                      // rules={[
                      //   {
                      //     required: true,
                      //     message: '请选择转派条件模板',
                      //   },
                      // ]}
                    />
                  )}
                  <ProFormSelect
                    label="审核条件模板"
                    disabled={params?.readonly || !taskType || disableDifferenceItemArriveShopTemplate}
                    name="templateDifferenceAuditId"
                    options={differenceItemArriveShopTemplateOptions}
                  />
                  <ProFormSelect
                    label="预警消息模板"
                    disabled={params?.readonly || !taskType}
                    name="messageWarningTemplateId"
                    options={MessageWarningEnableList}
                  />
                </>
              );
            }}
          </ProFormDependency>
        </ItemCard>
      )}

      {/* <OrganizationOrStoreSelectModal
        disabledTree
        open={modalProps.open}
        type={modalProps.type}
        value={modalProps.value}
        options={groupShopTreeOptions}
        onOk={(values) => {
          const shopIds = values?.map((item) => item?.value?.split('-')?.[1]);

          form?.setFieldValue('autoJoinShopIds', shopIds);

          setModalProps({
            open: false,
            type: ModalType.STORE,
          });
        }}
        onCancel={() => {
          setModalProps({
            type: ModalType.STORE,
            open: false,
          });
        }}
      /> */}
      <ModalForm
        title="批量选择门店"
        open={batchModalProps.open}
        width={480}
        modalProps={{
          destroyOnClose: true,
        }}
        onFinish={(values) => {
          const { name } = values;
          const keyArr = name?.trim()?.split(/,|，|\n|\r|\r\n|\n\r/) || [];
          const selectKeys = [];
          const notFounds = [];

          keyArr.forEach((key) => {
            if (shopOptions?.some((item) => item?.value === key)) {
              selectKeys.push(key);
            } else {
              notFounds.push(key);
            }
          });

          if (notFounds?.length) {
            message.error(`未找到${notFounds?.join('、')}等门店，请重新确认`);
          }

          const _shopIds = form?.getFieldValue('shopIds') || [];

          form?.setFieldValue('shopIds', [...new Set([..._shopIds, ...selectKeys])]);

          return Promise.resolve(true);
        }}
        onOpenChange={(value) => {
          if (!value) {
            setBatchModal({
              open: false,
            });
          }
        }}
      >
        <ProFormTextArea width="lg" placeholder="请输入要添加的门店编号，用中英文逗号隔开或换行隔开" name="name" />
      </ModalForm>
    </>
  );
};

export default BaseNode;
