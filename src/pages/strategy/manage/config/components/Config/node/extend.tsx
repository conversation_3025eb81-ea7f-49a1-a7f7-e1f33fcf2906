import { FC, useContext, useEffect, useMemo, useState } from 'react';
import {
  ProForm,
  ProFormDependency,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Edge } from '@antv/x6';
import { cloneDeep } from 'lodash';
import Synchrodata from './synchrodata';
import { StrategyConfigContext } from '../../..';
import { NodeEnum } from '../../flow-chart/node/common';
import ShopAutoJoin from '../../ShopAutoJoin';
import { NodeConfigProps } from '.';
import ItemCard from '@/components/item-card';
import OrganizationCascader from '@/components/organization-cascader';
import { StrategyTaskKind, StrategyTaskKindCN, TaskSubType } from '@/constants/strategy';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { getMessageWarningEnableList } from '@/http/apis/task-center';

const ExtendNode: FC<NodeConfigProps> = ({
  onTaskTypeChange,
  templateOptions,
  roleOptions,
  organizationOptions,
  shopOptions,
  onOrganizationChange,
  cell,
  edges,
  transferOptions,
  form,
}) => {
  const [params] = useQuerySearchParams();
  const { flowChart } = useContext(StrategyConfigContext);
  const [MessageWarningEnableList, setMessageWarningEnableList] = useState<any[]>([]);

  const getMessageOption = (taskType, taskSubType) => {
    setMessageWarningEnableList([]);

    form?.setFieldsValue({
      templateDifferenceAuditId: undefined,
    });
    cell?.setData(
      {
        ...cell?.getData(),
        templateDifferenceAuditId: undefined,
      },
      { overwrite: true },
    );

    getMessageWarningEnableList({ taskType, taskSubType }).then((res: any) => {
      setMessageWarningEnableList(
        () =>
          res?.map((m: any) => {
            return {
              label: m?.name,
              value: m?.id,
            };
          }) || [],
      );
    });
  };

  useEffect(() => {
    edges?.forEach((edge) => {
      const sourceCell = edge.getSourceCell();
      const sourceData = sourceCell?.getData();

      if (sourceCell.id !== cell.id && sourceData?.type === NodeEnum.基础任务 && sourceData?.templateId) {
        console.log('💀 ~ ExtendNode ~ sourceData:', sourceData);
        // flag = false;
        cell?.setData({
          ...cell?.getData(),
          baseTemplateId: sourceData?.templateId,
        });
      }

      if (sourceCell.id !== cell.id && sourceData?.type === NodeEnum.扩展任务 && sourceData?.baseTemplateId) {
        // flag = false;
        cell?.setData({
          ...cell?.getData(),
          baseTemplateId: sourceData?.baseTemplateId,
        });
      }
    });
    // if (!cell?.getData()?.baseTemplateId) {
    //   let flag = true;

    //   if (flag) {
    //     message.error('请选择基础任务');
    //   }
    // }
  }, []);

  const baseNode = useMemo(() => {
    /**
     *
     * @param edges 边集合
     * @param cellId 当前节点的id
     */
    const findBaseNodeLoopV2 = (edges: Edge[], cellId: string) => {
      const edgesClone = cloneDeep(edges);

      for (const edge of edgesClone?.reverse()) {
        const sourceCell = edge.getSourceCell();
        const sourceData = sourceCell?.getData();

        if (sourceCell.id === cellId) {
          continue;
        }

        if (sourceCell.id !== cellId && sourceData?.type === NodeEnum.基础任务) {
          console.log('💀 ~ findBaseNodeLoopV2 ~ sourceData:', sourceData);

          return sourceData;
        }

        // 如果直接上个节点没有基础节点, 则继续向上查找
        return findBaseNodeLoopV2(flowChart?.getConnectedEdges(sourceCell), sourceCell.id);
      }
    };

    return findBaseNodeLoopV2(edges, cell.id);
  }, [cell.id, edges, flowChart]);

  useEffect(() => {
    if (baseNode?.taskType) {
      if (baseNode?.taskType === TaskSubType.SELF && !baseNode?.taskSubType) {
        getMessageOption(baseNode?.taskType, 'SELF');

        return;
      }

      // 非自检 需要保证taskSubType不为空
      if (baseNode?.taskType !== TaskSubType.SELF && baseNode?.taskSubType) {
        getMessageOption(baseNode?.taskType, baseNode?.taskSubType);
      }
    }
  }, [baseNode?.taskType, baseNode?.taskSubType]);

  return (
    <>
      <ItemCard title="扩展任务设置">
        <ProFormText
          label="节点名称"
          name="nodeName"
          rules={[
            {
              max: 30,
              message: '请输入最多30个字的节点名称',
            },
          ]}
        />
        <ProFormSelect
          label="任务类型"
          name="taskType"
          // rules={[
          //   {
          //     required: true,
          //     message: '请选择任务类型',
          //   },
          // ]}
          disabled={!cell?.data?.baseTemplateId || params?.readonly}
          fieldProps={{
            onChange: (val: string) => {
              onTaskTypeChange?.({ type: val, baseTemplateId: cell.data.baseTemplateId });

              form?.setFieldsValue({
                templateId: undefined,
                transferTemplateId: undefined,
                templateDifferenceAuditId: undefined,
              });

              cell?.setData(
                {
                  ...cell?.getData(),

                  templateId: undefined,
                  transferTemplateId: undefined,
                  templateDifferenceAuditId: undefined,
                },
                { overwrite: true },
              );
            },
          }}
          valueEnum={{
            [StrategyTaskKind.REVIEW]: StrategyTaskKindCN[StrategyTaskKind.REVIEW],
            [StrategyTaskKind.ISSUE]: StrategyTaskKindCN[StrategyTaskKind.ISSUE],
          }}
        />
        <ProFormSelect
          label="任务选择"
          name="templateId"
          disabled={!cell?.data?.baseTemplateId || params?.readonly}
          placeholder="通过任务模板名称或ID模糊搜索"
          // rules={[
          //   {
          //     required: true,
          //     message: '请选择任务',
          //   },
          // ]}
          fieldProps={{
            showSearch: true,
            filterOption: (inputValue, options) => {
              if (inputValue) {
                const { value, label }: any = options;

                if (label?.includes(inputValue) || value?.toString()?.includes(inputValue)) {
                  return true;
                }
              }

              return false;
            },
            onChange: () => {
              form.setFieldsValue({
                transferTemplateId: undefined,
                templateDifferenceAuditId: undefined,
              });

              cell?.setData(
                {
                  ...cell?.getData(),

                  transferTemplateId: undefined,
                  templateDifferenceAuditId: undefined,
                },
                { overwrite: true },
              );
            },
          }}
          options={templateOptions}
        />
        <ProFormDigit
          name="priority"
          label="任务优先级"
          min={0}
          max={99}
          width="lg"
          placeholder="数字越大,优先级越低,最大值为99"
          extra={
            <div className="text-orange-500">温馨提示:同一策略配置多个点评模版时,以任务优先级高低触发唯一第一点评</div>
          }
        />
        {/* <ProForm.Item label="角色选择" name="roleIds">
          <RoleCascader options={roleOptions} multiple={true} />
        </ProForm.Item> */}
        <Synchrodata edges={edges} cell={cell} form={form} />
        <ProForm.Item label="组织选择" name="groupIds">
          <OrganizationCascader
            placeholder="请选择组织"
            options={organizationOptions}
            multiple={true}
            expandTrigger={'click'}
            onChange={(val) => {
              onOrganizationChange?.(val);
            }}
          />
        </ProForm.Item>
        <ProFormSelect
          label="门店选择"
          name="shopIds"
          options={shopOptions}
          fieldProps={{
            mode: 'multiple',
          }}
        />
        <ProFormRadio.Group
          label="包含筹备中和待营业的门店"
          name="readyShopFlag"
          required
          initialValue={false}
          options={[
            {
              label: '是',
              value: true,
            },
            {
              label: '否',
              value: false,
            },
          ]}
        />
        <ShopAutoJoin
          form={form}
          buttonClassName="mb-4"
          extra={
            <div className="text-orange-500">温馨提示：当基础任务为自检任务时，新营业门店自动纳入配置才会生效</div>
          }
        />
        <ItemCard title="任务操作条件">
          <ProFormDependency name={['taskType']}>
            {({ taskType }) => {
              return (
                <>
                  <ProFormSelect
                    label="转派条件模板"
                    disabled={params?.readonly || taskType !== 'REVIEW'}
                    name="transferTemplateId"
                    options={transferOptions}
                  />
                  <ProFormSelect
                    label="预警消息模板"
                    disabled={params?.readonly || !taskType}
                    name="messageWarningTemplateId"
                    options={MessageWarningEnableList}
                  />
                </>
              );
            }}
          </ProFormDependency>
        </ItemCard>
      </ItemCard>
    </>
  );
};

export default ExtendNode;
