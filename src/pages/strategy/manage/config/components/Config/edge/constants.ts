export enum EdgeTriggerType {
  ONCE = 'ONCE',
  CIRCULATION = 'CIRCULATION',
  // Custom = 'Custom',
  END = 'END',
  EVENT = 'EVENT',
  HIKVISION_EVENT = 'HIKVISION_EVENT',
}

export const EdgeTriggerTypeCN = {
  [EdgeTriggerType.CIRCULATION]: '循环',
  [EdgeTriggerType.ONCE]: '日期',
  [EdgeTriggerType.EVENT]: '事件',
  [EdgeTriggerType.HIKVISION_EVENT]: '海康项目',
  // [EdgeTriggerType.Custom]: '接口自定义',
  // [EdgeTriggerType.END]: '完成',
};

export enum TaskExecuteWayEnum {
  INSTANTLY = 'INSTANTLY',
  CUSTOMIZE = 'CUSTOMIZE',
}

export const delayExecutedMap = {
  [TaskExecuteWayEnum.INSTANTLY]: false,
  [TaskExecuteWayEnum.CUSTOMIZE]: true,
};

export const TaskExecuteWayTypeCN = {
  [TaskExecuteWayEnum.INSTANTLY]: '生效即可执行',
  [TaskExecuteWayEnum.CUSTOMIZE]: '按执行时间',
};

// BE代表后端数据
export const EdgeTriggerTypeBE: Record<EdgeTriggerType, string> = {
  [EdgeTriggerType.CIRCULATION]: 'TIMER',
  [EdgeTriggerType.ONCE]: 'TIMER',
  [EdgeTriggerType.EVENT]: 'EVENT',
  [EdgeTriggerType.END]: 'EVENT',
  [EdgeTriggerType.HIKVISION_EVENT]: 'EVENT',
};

// export enum EdgeJudgeType {
//   Score = 'Score',
//   Custom = 'Custom',
//   None = 'None',
// }

// export const EdgeJudgeTypeCN: Record<EdgeJudgeType, string> = {
//   [EdgeJudgeType.Score]: '评分',
//   [EdgeJudgeType.Custom]: '数据自定义',
//   [EdgeJudgeType.None]: '无',
// };

export enum EdgeJudgeScoreDataSource {
  Checklist = 'Checklist',
  CheckItem = 'CheckItem',
  Custom = 'Custom',
}

export const EdgeJudgeScoreDataSourceCN: Record<EdgeJudgeScoreDataSource, string> = {
  [EdgeJudgeScoreDataSource.Checklist]: '检查表分数',
  [EdgeJudgeScoreDataSource.CheckItem]: '检查项分数',
  [EdgeJudgeScoreDataSource.Custom]: '自定义',
};

export enum EdgeJudgeScoreMethod {
  MoreThan = 'MoreThan',
  MoreAndEqual = 'MoreAndEqual',
  Equal = 'Equal',
  LessAndEqual = 'LessAndEqual',
  LessThan = 'LessThan',
}
export const EdgeJudgeScoreMethodCN: Record<EdgeJudgeScoreMethod, string> = {
  [EdgeJudgeScoreMethod.MoreThan]: '大于',
  [EdgeJudgeScoreMethod.MoreAndEqual]: '大于等于',
  [EdgeJudgeScoreMethod.Equal]: '等于',
  [EdgeJudgeScoreMethod.LessAndEqual]: '小于等于',
  [EdgeJudgeScoreMethod.LessThan]: '小于',
};

export const EdgeJudgeScoreMethodSymbol: Record<EdgeJudgeScoreMethod, string> = {
  [EdgeJudgeScoreMethod.MoreThan]: '>',
  [EdgeJudgeScoreMethod.MoreAndEqual]: '>=',
  [EdgeJudgeScoreMethod.Equal]: '==',
  [EdgeJudgeScoreMethod.LessAndEqual]: '<=',
  [EdgeJudgeScoreMethod.LessThan]: '<',
};

/** 开业进度节点选项 */
export const engineeringProgressOptions = [
  {
    label: '开工令下发',
    value: 'START_WORK_ORDER_ISSUED',
  },
  {
    label: '隐蔽验收开始',
    value: 'HIDDEN_ACCEPTANCE_STARTED',
  },
  {
    label: '提交竣工验收报告',
    value: 'SUBMIT_COMPLETION_ACCEPTANCE_REPORT',
  },
  {
    label: '提交食安许可证时间',
    value: 'SUBMIT_BUSINESS_LICENSE_TIME',
  },
  {
    label: '预计开业时间-3天',
    value: 'EXPECTED_OPENING_TIME_MINUS_THREE_DAYS',
  },
];

/** 交接方节点选项 */
export const partyOptions = [
  {
    label: '中台-人员交接事件-移交方',
    value: 'HANDOVER_PARTY',
  },
  {
    label: '中台-人员交接事件-承接方',
    value: 'RECEIVING_PARTY',
  },
];

/** 判断规则枚举 */
export enum JudgeRuleEnum {
  报告得分 = 1,
  门店诊断等级 = 6,
  检查项结果 = 10,
  '不合格/逾期循环自检最大次数' = 13,
  任务逾期 = 14,
  宣享小盒未付款 = 15,
}
