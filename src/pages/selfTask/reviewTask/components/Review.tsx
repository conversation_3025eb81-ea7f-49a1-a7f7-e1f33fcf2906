/* eslint-disable max-lines-per-function */
import { type Key, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { type ActionType, ProCard, ProColumns, ProForm, ProTable } from '@ant-design/pro-components';
import { useDebounceFn } from 'ahooks';
import { Button, message, Space, Spin } from 'antd';
import dayjs from 'dayjs';
import { debounce, isBoolean, isEmpty } from 'lodash';
import { ModifyTaskUserModal } from './ModifyTaskUserModal';
import ITimePicker, { DateQueryType } from './TimePicker';
import Service, { RequestName } from '../service';
import { DownloadButton } from '@/components/DownloadButton';
import { Permission } from '@/components/Permission';
import PreviousPageAndNextPageButton from '@/components/PreviousPageAndNextPage/Button';
import {
  getChecklistColumn,
  getColumns,
  getPersonColumn,
  getRoleColumn,
  getStoreColumn,
  getTaskColumn,
} from '@/components/pro-table-config';
import { getTaskTemplateColumn } from '@/components/pro-table-config/task-template';
import { Auth } from '@/constants/auth';
import { ChecklistStrategyType } from '@/constants/checklist-strategy';
import { NotFilledItemHandleType, StrategyTaskStatus } from '@/constants/strategy';
import { InspectionReviewStatus } from '@/constants/task';
import { TemplateStatus } from '@/constants/template';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import useUserListCaching from '@/hooks/use-user-list-caching';
import { usePermission } from '@/hooks/usePermission';
import { getAwaitNextReviewTaskId, getNextReviewTaskId, startReviewTask } from '@/http/apis/template';
import TaskDetailDrawer from '@/pages/reportCenter/components/task-detail-drawer';
import useTransferModal from '@/pages/taskCenter/cloud/hooks/use-transfer-modal';
import { parserParams } from '@/utils/convert';
import { formatDateToUTC } from '@/utils/date';
import { disabled30Date } from '@/utils/disabled';
import type { PaginationParams } from '@/utils/pagination';

const Review = ({
  /** 是否为待处理 */
  isAwaitDeal = false,
}) => {
  const [urlParams, setUrlParams] = useQuerySearchParams();
  const _reviewTask = parserParams(urlParams?.reviewTask || {});
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({ pageNo: 1, pageSize: 10 });
  const [awaitDealPaginationParams, setAwaitDealPaginationParams] = useState<PaginationParams>({
    pageNo: 1,
    pageSize: 10,
  });
  const [form] = ProForm.useForm();
  const [service, executeRequest, dispatch] = Service();
  const actionRef = useRef<ActionType>();
  const { showModal } = useTransferModal();
  const hasPermission = usePermission();

  const hasModifyUserPermission = hasPermission(Auth.任务中心_我的任务_点评任务_修改执行人);
  const [selectKeys, setSelectKeys] = useState<Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [drawerProps, setDrawerProps] = useState<{
    open: boolean;
    data?: any;
    hasReview?: boolean;
    readonly?: boolean;
    isPreview?: boolean;
    reviewSumUp?: string;
    notFilledItemHandleType?: NotFilledItemHandleType;
    detailType?: 'PATROL' | 'SELF';
  }>({
    open: false,
  });

  const { pagination, setPagination } = useMemo(() => {
    const pagination = isAwaitDeal ? awaitDealPaginationParams : paginationParams;
    const setPagination = isAwaitDeal ? setAwaitDealPaginationParams : setPaginationParams;

    return {
      pagination,
      setPagination,
    };
  }, [awaitDealPaginationParams, isAwaitDeal, paginationParams]);

  const getTaskInfo = async ({ taskId, readonly, hasNeedReview, notFilledItemHandleType }: any) => {
    const info = await executeRequest(RequestName.GetStrategyTaskInfo, { id: taskId });
    const worksheets = await executeRequest(RequestName.GetStrategyTaskWorksheet, {
      id: taskId,
      hasReview: readonly ? false : hasNeedReview,
      notFilledItemHandleType,
    });
    const summarys = await executeRequest(RequestName.GetStrategyTaskReviewSummary, taskId);
    const statistics = await executeRequest(RequestName.GetStrategyTaskStatistics, { id: taskId });

    return { info, worksheets: worksheets?.data, summarys, statistics };
  };

  const [showReportLoading, setShowReportLoading] = useState(false);

  const rs = useDebounceFn(() => {}, { wait: 300 });

  const showReport = async (record, readonly) => {
    try {
      setShowReportLoading(true);

      const data = await getTaskInfo({
        taskId: record?.baseTaskId,
        readonly,
        hasNeedReview: record?.hasReviewPermission,
      });

      setDrawerProps({
        open: true,
        data,
        hasReview: readonly ? false : record?.hasReviewPermission,
        readonly,
        detailType: record?.taskSubType === 'SELF' ? 'SELF' : 'PATROL',
      });
      setShowReportLoading(false);
    } catch (error) {
      setShowReportLoading(false);
    }
  };

  const requestChecklist = (labelIds?: any) => {
    return executeRequest(RequestName.GetChecklistSimplelist, {
      labelIds,
      worksheetTypes: [
        ChecklistStrategyType.PATROL,
        ChecklistStrategyType.DIFFERENT_ITEM_PATROL,
        ChecklistStrategyType.SELF,
      ],
    });
  };

  const formatFormValue = (value: any) => {
    const timeMap = {
      [DateQueryType.ReportSubmissionTime]: ['reportSubmitTimeStart', 'reportSubmitTimeEnd'],
      [DateQueryType.TaskStartTime]: ['taskStartTimeStart', 'taskStartTimeEnd'],
      [DateQueryType.TaskSubmitTime]: ['taskExpiredTimeStart', 'taskExpiredTimeEnd'],
    };

    const { store, dateQuery, sheet, ...rest } = value;
    const { checklist } = sheet || {};
    const { date, type } = dateQuery || {};
    const [startDate, endDate] = date || [];

    const time = isAwaitDeal
      ? {}
      : {
          [timeMap[type][0]]: formatDateToUTC(startDate),
          [timeMap[type][1]]: formatDateToUTC(endDate),
        };

    return {
      ...store,
      ...rest,
      worksheetId: checklist,
      ...time,
      // 是否过滤点评人数据，管理人员传false
      filterReviewUser: !hasModifyUserPermission,
      // 无需数量统计（不做分页，改为上一页、下一页）
      hasCount: false,
    };
  };

  const { userListCachingRef } = useUserListCaching({ options: service?.reviewerOptions });

  const debounceSearch = useCallback(
    debounce((value: string) => {
      if (value?.trim()) {
        setUrlParams({ ...urlParams, strategyName: value });
        executeRequest(RequestName.GetStrategyManageList, { strategyName: value, pageNo: 1, pageSize: 20 });
      }
    }, 500),
    [],
  );

  const columns = getColumns({
    searchColumns: [
      getStoreColumn({
        organizationOptions: service?.organizationOptions,
        storeOptions: service?.storeOptions,
        onStoreFocus: (groupId: any) => {
          executeRequest(RequestName.GetStoreList, {
            fightGroupId: groupId,
            groupType: 2,
            privilegeCode: 1,
          });
        },
      }),
      getChecklistColumn({
        tagOptions: service?.tagOptions,
        checklistOptions: service?.checklistOptions,
        onTagChange: (value) => {
          requestChecklist(value);
        },
      }),
      {
        title: '日期查询',
        dataIndex: 'dateQuery',
        hideInTable: true,
        colSize: 2,
        renderFormItem: () => {
          return (
            <ITimePicker
              disabledDate={disabled30Date}
              typeOptions={[
                {
                  value: DateQueryType.ReportSubmissionTime,
                  label: '报告提交时间',
                },
                {
                  value: DateQueryType.TaskStartTime,
                  label: '任务开始时间',
                },
                {
                  value: DateQueryType.TaskSubmitTime,
                  label: '任务截止时间',
                },
              ]}
            />
          );
        },
        initialValue: {
          type: DateQueryType.TaskSubmitTime,
          dateType: undefined,
          date: [dayjs().add(-4, 'days').startOf('day'), dayjs().add(2, 'days').endOf('day')],
        },
      },
      getTaskTemplateColumn({ options: service?.templateOptions }, { dataIndex: 'templateId', title: '点评模板' }),
      // getReportStatusColumn(
      //   {
      //     valueEnum: {
      //       WAITING_SUBMIT: '待提交',
      //       WAITING_CONFIRM: '待确认',
      //       CONFIRMED: '已确认',
      //       CANCELED: '已作废',
      //     },
      //   },
      //   { dataIndex: 'reportStatus', title: '报告状态' },
      // ),
      // getReportPassedColumn(null, { title: '报告是否通过', dataIndex: 'reportPassed' }),
      getPersonColumn(
        {
          options: userListCachingRef?.current || service?.reviewerOptions,
          onFocus: () => {
            if (!userListCachingRef?.current) {
              executeRequest(RequestName.GetUserInfoList);
            }
          },
          loading: service?.reviewerLoading,
        },
        { dataIndex: 'taskUser', title: '点评人' },
      ),
      // {
      //   title: '点评状态',
      //   dataIndex: 'reviewStatus',
      //   colSize: 1,
      //   valueType: 'select',
      //   valueEnum: {
      //     REVIEWED: '已点评',
      //     NOT_REVIEWED: '待点评',
      //     REVIEWED_EXPIRE: '点评超时',
      //   },
      // },
      {
        title: '任务状态',
        dataIndex: 'taskStatus',
        // initialValue: 'WAITING_START',
        valueEnum: {
          WAITING_START: '待开始',
          TRANSFER_AUDIT: '任务转办审核中',
          COMPLETED: '已完成',
          CANCELED: '已作废',
          EXPIRED: '已逾期',
        },
        valueType: 'select',
      },

      getTaskColumn(
        {
          options: service?.strategNameList,
          placeholder: '请输入策略名称',
          loading: service?.strategyLoading,
          onSearch: (val) => {
            debounceSearch(val);
          },
          onClear: () => {
            dispatch({
              strategNameList: [],
            });
          },
        },
        {
          dataIndex: 'strategyId',
          title: '策略名称',
        },
      ),
      getRoleColumn(
        {
          options: service?.roleOptions,
        },
        { dataIndex: 'reviewRoleId', title: '点评角色' },
      ),
    ].filter(
      (f) =>
        !isAwaitDeal || !['dateQuery', 'taskUser', 'taskStatus', 'strategyId', 'reviewRoleId'].includes(f.dataIndex),
    ) as ProColumns[],
    tableColumns: [
      {
        title: '策略名称',
        dataIndex: 'strategyName',
      },
      {
        title: '批次ID',
        dataIndex: 'batchId',
      },
      {
        title: '任务名称',
        dataIndex: 'taskName',
      },

      {
        title: '门店id',
        dataIndex: 'shopId',
      },
      {
        title: '门店名称',
        dataIndex: 'shopName',
      },
      {
        title: '基础任务类型',
        key: 'taskSubType2',
        render: (_, { taskSubType }) => {
          return taskSubType === 'SELF' ? '自检' : '巡检';
        },
      },
      {
        title: '巡检类型',
        dataIndex: 'taskSubType',
        valueEnum: {
          SELF: '自检',
          NORMAL: '到店巡检',
          VIDEO: '视频云巡检',
          AI: 'AI巡检',
          FOOD_SAFETY_NORMAL: '食安线下稽核',
          FOOD_SAFETY_VIDEO: '食安线上稽核',
          DIAGNOSTIC: '诊断巡检',
          FOOD_SAFETY_ARRIVE_SHOP: '食安稽核到店辅导',
          DIFFERENCE_ITEM_ARRIVE_SHOP: '差异项到店任务',
        },
        render: (dom, { taskSubType }) => {
          return taskSubType === 'SELF' ? '-' : dom;
        },
      },
      {
        title: '任务状态',
        dataIndex: 'taskStatus',
        valueEnum: {
          WAITING_START: '待开始',
          TRANSFER_AUDIT: '任务转办审核中',
          COMPLETED: '已完成',
          CANCELED: '已作废',
          EXPIRED: '已逾期',
        },
      },
      {
        title: '点评人',
        dataIndex: 'taskUserName',
      },
      {
        title: '报告提交时间',
        dataIndex: 'reportSubmitTime',
        render: (_, { reportSubmitTime }) =>
          reportSubmitTime ? dayjs(reportSubmitTime).format('YYYY-MM-DD HH:mm:ss') : '-',
      },
      {
        title: '任务过期时间',
        dataIndex: 'expiredTime',
        render: (_, { expiredTime }) => (expiredTime ? dayjs(expiredTime).format('YYYY-MM-DD HH:mm:ss') : '-'),
      },
      {
        title: '点评开始时间',
        dataIndex: 'taskStartTime',
        render: (_, { taskStartTime }) => (taskStartTime ? dayjs(taskStartTime).format('YYYY-MM-DD HH:mm:ss') : '-'),
      },
      {
        title: '第一点评状态',
        dataIndex: 'reviewStatus',
        valueEnum: {
          REVIEWED: '已点评',
          NOT_REVIEWED: '待点评',
          REVIEWED_EXPIRE: '点评超时',
          NO_NEEDED_REVIEWS: '无需点评',
        },
      },
      {
        title: '第一点评是否通过',
        dataIndex: 'reviewPassed',
        render: (_, { reviewPassed, reviewStatus }) => {
          if (
            [InspectionReviewStatus.NO_NEEDED_REVIEWS, InspectionReviewStatus.REVIEWED_EXPIRE].includes(reviewStatus)
          ) {
            return '-';
          }

          if (!isBoolean(reviewPassed)) {
            return '-';
          }

          if (reviewPassed) {
            return '通过';
          }

          return '不通过';
        },
      },
      {
        title: '点评提交时间',
        dataIndex: 'reviewSubmitTime',
        render: (_, { reviewSubmitTime }) =>
          reviewSubmitTime ? dayjs(reviewSubmitTime).format('YYYY-MM-DD HH:mm:ss') : '-',
      },

      {
        title: '点评时长',
        dataIndex: 'reviewDuration',
        render: (_, { reviewDuration }) => {
          return reviewDuration ? `${Math.floor(reviewDuration / 1000)}s` : '-';
        },
      },
      // {
      //   title: '第一点评时间',
      //   dataIndex: 'reviewTime',
      //   render: (_, { reviewTime }) => (reviewTime ? dayjs(reviewTime).format('YYYY-MM-DD HH:mm:ss') : '-'),
      // },
      {
        title: '操作',
        fixed: 'right',
        render: (_, record) => {
          return (
            <Space>
              <a onClick={() => showReport(record, true)}>查看详情</a>
              {isAwaitDeal &&
                [StrategyTaskStatus.WAITING_START].includes(record?.taskStatus) &&
                record?.hasReviewPermission && <a onClick={() => showReport(record, false)}>点评</a>}
              {[StrategyTaskStatus.WAITING_START].includes(record?.taskStatus) && record?.hasTransfer && (
                <a
                  onClick={() =>
                    showModal({
                      initialValues: {
                        taskIds: [record?.taskId],
                      },
                      onOk: (values) => {
                        return executeRequest(RequestName.ApplyBatchTransfer, values).then(() => {
                          message.success('转派成功');
                          actionRef.current.reload();
                        });
                      },
                    })
                  }
                >
                  任务转派
                </a>
              )}
            </Space>
          );
        },
      },
    ],
  });

  useEffect(() => {
    executeRequest(RequestName.GetGroupTreeList);
    executeRequest(RequestName.GetTags);
    executeRequest(RequestName.GetRoleCascaderData, 1);
    requestChecklist();
    executeRequest(RequestName.QueryBaseTemplateList, {
      status: TemplateStatus.EFFECTIVE,
      type: 'REVIEW',
    });

    if (!isEmpty(_reviewTask)) {
      const { store, dateQuery, sheet, reportPassed, ...rest } = _reviewTask;
      const { groupId, shopIds } = store || {};

      if (_reviewTask?.taskUser) {
        executeRequest(RequestName.GetUserInfoList);
      }

      if (urlParams?.strategyName) {
        executeRequest(RequestName.GetStrategyManageList, {
          strategyName: urlParams?.strategyName,
          pageNo: 1,
          pageSize: 20,
        });
      }

      if (shopIds?.length) {
        executeRequest(RequestName.GetStoreList, {
          fightGroupId: groupId,
          groupType: 2,
          privilegeCode: 1,
        });
      }

      form.setFieldsValue({
        ...rest,
        store,
        dateQuery: {
          type: +dateQuery?.type || DateQueryType.TaskSubmitTime,
          date:
            dateQuery?.date?.[0] && dateQuery?.date?.[1]
              ? [dayjs(dateQuery.date[0]), dayjs(dateQuery.date[1])]
              : [dayjs().subtract(4, 'days').startOf('day'), dayjs().add(2, 'days').endOf('day')],
        },
        sheet: {
          tags: sheet?.tags?.map((item) => +item),
          checklist: sheet?.checklist ? +sheet?.checklist : undefined,
        },
        reportPassed: reportPassed ? reportPassed?.toString() : undefined,
      });

      /* setPaginationParams({
        pageNo: rest?.pageNo || 1,
        pageSize: rest?.pageSize || 10,
      }); */
    }

    form.submit();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Spin spinning={showReportLoading}>
      <ProTable
        columns={columns}
        rowKey="taskId"
        scroll={{ x: 2500 }}
        search={{ form, collapsed: false, collapseRender: false, labelWidth: 100 }}
        params={pagination}
        request={async (value) => {
          const { current, pageSize, ...val } = value;

          const payload = {
            ...formatFormValue(val),
            pageNo: current || _reviewTask?.current || 1,
            pageSize: pageSize || _reviewTask?.pageSize || 10,
            ...pagination,
          };

          setUrlParams({
            ...urlParams,
            reviewTask: {
              ...val,
              pageNo: current || _reviewTask?.current || 1,
              pageSize: pageSize || _reviewTask?.pageSize || 10,
              ...pagination,
            },
          });

          setPagination({
            pageNo: current || _reviewTask?.current || 1,
            pageSize: pageSize || _reviewTask?.pageSize || 10,
            ...pagination,
          });

          const res = await executeRequest(
            isAwaitDeal ? RequestName.QueryAwaitReviewTaskList : RequestName.QueryReviewTaskList,
            payload,
          );

          return {
            data: res?.data,
            success: true,
            total: res?.total,
          };
        }}
        onSubmit={() => {
          setPagination((p) => ({ ...p, pageNo: 1 }));
        }}
        onReset={() => {
          setPagination((p) => ({ ...p, pageNo: 1 }));
          dispatch({
            strategNameList: [],
          });
          actionRef?.current?.reload();
        }}
        actionRef={actionRef}
        manualRequest={true}
        options={false}
        /* pagination={{
          showSizeChanger: true,
          current: paginationParams.pageNo,
          pageSize: paginationParams.pageSize,
        }}
        onChange={({ current, pageSize }) => {
          setPaginationParams((p) => ({
            ...p,
            pageNo: current ?? 1,
            pageSize: pageSize ?? 10,
          }));
        }} */
        pagination={false}
        tableRender={(props: any, _d, { table }) => {
          return (
            <ProCard>
              <div className="mb-2 flex justify-between">
                <Space>
                  {!isAwaitDeal && (
                    <Permission permission={Auth.任务中心_我的任务_点评任务_修改执行人}>
                      <Button
                        type="primary"
                        disabled={selectKeys.length === 0}
                        onClick={() => {
                          ModifyTaskUserModal.showModal({
                            data: {
                              taskIds: selectKeys,
                            },
                            onSuccess: () => {
                              actionRef.current.reload();
                            },
                          });
                        }}
                      >
                        修改执行人
                      </Button>
                    </Permission>
                  )}
                  <Button
                    disabled={selectKeys.length === 0}
                    onClick={() => {
                      const transferTasksIds = selectedRows
                        .filter((item) => item?.hasTransfer)
                        ?.map((item) => item?.taskId);

                      if (transferTasksIds.length === 0) {
                        message.warning('选择的任务都无法进行转派');

                        return;
                      }

                      showModal({
                        initialValues: {
                          taskIds: transferTasksIds,
                        },
                        onOk: (values) => {
                          return executeRequest(RequestName.ApplyBatchTransfer, values).then(() => {
                            message.success('转派成功');
                            actionRef.current.reload();
                          });
                        },
                      });
                    }}
                  >
                    批量转派
                  </Button>
                </Space>
                {!isAwaitDeal && (
                  <DownloadButton
                    downloadReq={() => {
                      const values = form.getFieldsValue();

                      return executeRequest(RequestName.ExportReviewTaskList, formatFormValue(values));
                    }}
                  >
                    下载明细
                  </DownloadButton>
                )}
              </div>
              {table}
              <PreviousPageAndNextPageButton
                pageNum={pagination.pageNo}
                pageSize={pagination.pageSize}
                dataLength={props?.action?.dataSource?.length ?? 0}
                loading={props?.action?.loading}
                onChange={({ pageNum, pageSize }) => {
                  setPagination((p) => ({
                    ...p,
                    pageNo: pageNum,
                    pageSize,
                  }));
                }}
              />
            </ProCard>
          );
        }}
        rowSelection={{
          selectedRowKeys: selectKeys,
          type: 'checkbox',
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectKeys(selectedRowKeys);
            setSelectedRows(selectedRows);
          },
          getCheckboxProps: (record) => ({
            disabled: ![StrategyTaskStatus.WAITING_START].includes(record?.taskStatus),
          }),
        }}
      />
      <TaskDetailDrawer
        open={drawerProps?.open}
        data={drawerProps?.data}
        onClose={() => setDrawerProps({ open: false })}
        hasReview={drawerProps?.hasReview}
        saveReviewItem={(data) => {
          return executeRequest(RequestName.SaveReviewItem, data);
        }}
        reviewSumUp={drawerProps?.reviewSumUp}
        isPreview={drawerProps?.isPreview}
        detailType={drawerProps?.detailType}
        readonly={drawerProps?.readonly}
        readOnlyReformLimit
        previewReport={async (data: any) => {
          const taskData = await getTaskInfo({ taskId: data?.taskId, readonly: true, hasNeedReview: data?.hasReview });

          setDrawerProps({
            open: true,
            data: taskData,
            hasReview: data?.hasReview,
            readonly: true,
            isPreview: true,
            reviewSumUp: data?.reviewSumUp,
            notFilledItemHandleType: data?.notFilledItemHandleType,
            detailType: data?.taskSubType === 'SELF' ? 'SELF' : 'PATROL',
          });
        }}
        submitReview={async ({ summary, notFilledItemHandleType }: any) => {
          await executeRequest(RequestName.SubmitReviewSummary, { taskId: drawerProps?.data?.info?.taskId, summary });
          await executeRequest(RequestName.SubmitReviewTask, {
            baseTaskId: drawerProps?.data?.info?.taskId,
            notFilledItemHandleType: notFilledItemHandleType || NotFilledItemHandleType.SET_FULL_SCORE,
          });
          setDrawerProps({
            open: false,
          });
          actionRef.current.reload();
        }}
        submitNextReview={async ({ summary, notFilledItemHandleType }) => {
          try {
            setShowReportLoading(true);
            await executeRequest(RequestName.SubmitReviewSummary, { taskId: drawerProps?.data?.info?.taskId, summary });
            await executeRequest(RequestName.SubmitReviewTask, {
              baseTaskId: drawerProps?.data?.info?.taskId,
              notFilledItemHandleType: notFilledItemHandleType || NotFilledItemHandleType.SET_FULL_SCORE,
            });

            const formData = form.getFieldsValue();

            const requestFn = isAwaitDeal ? getAwaitNextReviewTaskId : getNextReviewTaskId;

            const res = await requestFn(formatFormValue(formData));

            if (!res) {
              message.warning('没有下一个点评任务');

              setDrawerProps({ open: false });

              actionRef.current.reload();

              setShowReportLoading(false);

              return;
            }

            showReport({ ...res, hasReviewPermission: true }, false);

            actionRef.current.reload();
          } catch (error) {
            setShowReportLoading(false);
          }
        }}
        width={1000}
        onWorksheetChangeCallback={(changeData: any) => {
          if (changeData?.reviewTaskId && !drawerProps?.readonly) {
            startReviewTask({ taskId: changeData?.reviewTaskId });
          }
        }}
        loading={showReportLoading}
      />
      <ModifyTaskUserModal />
    </Spin>
  );
};

export default Review;
