/* eslint-disable max-lines-per-function */
import { type Key, useEffect, useMemo, useRef, useState } from 'react';
import { type ActionType, ProCard, ProColumns, ProForm, ProTable } from '@ant-design/pro-components';
import { Button, message, Space } from 'antd';
import dayjs from 'dayjs';
import { isBoolean, isEmpty } from 'lodash';
import { ConfirmedDescModal } from './components/ConfirmedDescModal';
import { OperationType } from './CounselingReview';
import Service, { RequestName } from '../../service';
import { ModifyTaskUserModal } from '../ModifyTaskUserModal';
import ITimePicker, { DateQueryType } from '../TimePicker';
import { DownloadButton } from '@/components/DownloadButton';
import { Permission } from '@/components/Permission';
import PreviousPageAndNextPageButton from '@/components/PreviousPageAndNextPage/Button';
import { getChecklistColumn, getColumns, getPersonColumn, getStoreColumn } from '@/components/pro-table-config';
import { getTaskTemplateColumn } from '@/components/pro-table-config/task-template';
import { Auth } from '@/constants/auth';
import { ChecklistStrategyType } from '@/constants/checklist-strategy';
import { StrategyTaskStatus } from '@/constants/strategy';
import { InspectionReviewStatus } from '@/constants/task';
import { TemplateStatus } from '@/constants/template';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import useUserListCaching from '@/hooks/use-user-list-caching';
import { usePermission } from '@/hooks/usePermission';
import useTransferModal from '@/pages/taskCenter/cloud/hooks/use-transfer-modal';
import { RouteKey } from '@/router/config';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { parserParams } from '@/utils/convert';
import { formatDateToUTC } from '@/utils/date';
import { disabled30Date } from '@/utils/disabled';
import type { PaginationParams } from '@/utils/pagination';

const Counseling = ({
  /** 是否为待处理 */
  isAwaitDeal = false,
}) => {
  const [urlParams, setUrlParams] = useQuerySearchParams();
  const _counseling = parserParams(urlParams?.counseling || {});
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({ pageNo: 1, pageSize: 10 });
  const [awaitDealPaginationParams, setAwaitDealPaginationParams] = useState<PaginationParams>({
    pageNo: 1,
    pageSize: 10,
  });
  const [form] = ProForm.useForm();
  const [service, executeRequest] = Service();
  const actionRef = useRef<ActionType>();
  const { showModal } = useTransferModal();
  const hasPermission = usePermission();
  const navigate = useGlobalNavigate();

  const hasModifyUserPermission = hasPermission(Auth.任务中心_我的任务_食安稽核到店辅导点评任务_修改执行人);
  const [selectKeys, setSelectKeys] = useState<Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);

  const { pagination, setPagination } = useMemo(() => {
    const pagination = isAwaitDeal ? awaitDealPaginationParams : paginationParams;
    const setPagination = isAwaitDeal ? setAwaitDealPaginationParams : setPaginationParams;

    return {
      pagination,
      setPagination,
    };
  }, [awaitDealPaginationParams, isAwaitDeal, paginationParams]);

  const requestChecklist = (labelIds?: any) => {
    return executeRequest(RequestName.GetChecklistSimplelist, {
      labelIds,
      worksheetTypes: [ChecklistStrategyType.PATROL],
    });
  };

  const formatFormValue = (value: any) => {
    const timeMap = {
      [DateQueryType.ReportSubmissionTime]: ['reportSubmitTimeStart', 'reportSubmitTimeEnd'],
      [DateQueryType.TaskStartTime]: ['taskStartTimeStart', 'taskStartTimeEnd'],
      [DateQueryType.TaskSubmitTime]: ['taskExpiredTimeStart', 'taskExpiredTimeEnd'],
    };

    const { store, dateQuery, sheet, ...rest } = value;
    const { checklist } = sheet || {};
    const { date, type } = dateQuery || {};
    const [startDate, endDate] = date || [];

    const time = isAwaitDeal
      ? {}
      : {
          [timeMap[type][0]]: formatDateToUTC(startDate),
          [timeMap[type][1]]: formatDateToUTC(endDate),
        };

    return {
      ...store,
      ...rest,
      worksheetId: checklist,
      ...time,
      // 是否过滤点评人数据，管理人员传false
      filterReviewUser: !hasModifyUserPermission,
      // 无需数量统计（不做分页，改为上一页、下一页）
      hasCount: false,
    };
  };

  const { userListCachingRef } = useUserListCaching({ options: service?.reviewerOptions });

  const columns = getColumns({
    searchColumns: [
      getStoreColumn({
        organizationOptions: service?.organizationOptions,
        storeOptions: service?.storeOptions,
        onStoreFocus: (groupId: any) => {
          executeRequest(RequestName.GetStoreList, {
            fightGroupId: groupId,
            groupType: 2,
            privilegeCode: 1,
          });
        },
      }),
      getChecklistColumn({
        tagOptions: service?.tagOptions,
        checklistOptions: service?.checklistOptions,
        onTagChange: (value) => {
          requestChecklist(value);
        },
      }),
      {
        title: '日期查询',
        dataIndex: 'dateQuery',
        hideInTable: true,
        colSize: 2,
        renderFormItem: () => {
          return (
            <ITimePicker
              disabledDate={disabled30Date}
              typeOptions={[
                {
                  value: DateQueryType.ReportSubmissionTime,
                  label: '报告提交时间',
                },
                {
                  value: DateQueryType.TaskStartTime,
                  label: '任务开始时间',
                },
                {
                  value: DateQueryType.TaskSubmitTime,
                  label: '任务截止时间',
                },
              ]}
            />
          );
        },
        initialValue: {
          type: DateQueryType.TaskSubmitTime,
          dateType: undefined,
          date: [dayjs().add(-4, 'days').startOf('day'), dayjs().add(2, 'days').endOf('day')],
        },
      },
      getTaskTemplateColumn({ options: service?.templateOptions }, { dataIndex: 'templateId', title: '点评模板' }),
      // getReportStatusColumn(
      //   {
      //     valueEnum: {
      //       WAITING_SUBMIT: '待提交',
      //       WAITING_CONFIRM: '待确认',
      //       CONFIRMED: '已确认',
      //       CANCELED: '已作废',
      //     },
      //   },
      //   { dataIndex: 'reportStatus', title: '报告状态' },
      // ),
      // getReportPassedColumn(null, { title: '报告是否通过', dataIndex: 'reportPassed' }),
      getPersonColumn(
        {
          options: userListCachingRef?.current || service?.reviewerOptions,
          onFocus: () => {
            if (!userListCachingRef?.current) {
              executeRequest(RequestName.GetUserInfoList);
            }
          },
          loading: service?.reviewerLoading,
        },
        { dataIndex: 'taskUser', title: '点评人', hideInSearch: !hasModifyUserPermission },
      ),
      // {
      //   title: '点评状态',
      //   dataIndex: 'reviewStatus',
      //   colSize: 1,
      //   valueType: 'select',
      //   valueEnum: {
      //     REVIEWED: '已点评',
      //     NOT_REVIEWED: '待点评',
      //     REVIEWED_EXPIRE: '点评超时',
      //   },
      // },
      {
        title: '任务状态',
        dataIndex: 'taskStatus',
        // initialValue: 'WAITING_START',
        valueEnum: {
          WAITING_START: '待开始',
          TRANSFER_AUDIT: '任务转办审核中',
          COMPLETED: '已完成',
          CANCELED: '已作废',
          EXPIRED: '已逾期',
        },
        valueType: 'select',
      },
      {
        title: '门店类型',
        dataIndex: 'jhType',
        valueEnum: { ABNORMAL: '不合格门店', RECHECK: '复检门店' },
      },
    ].filter((f) => !isAwaitDeal || !['dateQuery', 'taskUser', 'taskStatus'].includes(f.dataIndex)) as ProColumns[],
    tableColumns: [
      {
        title: '策略名称',
        dataIndex: 'strategyName',
      },
      {
        title: '批次ID',
        dataIndex: 'batchId',
      },
      {
        title: '任务名称',
        dataIndex: 'taskName',
      },

      {
        title: '门店id',
        dataIndex: 'shopId',
      },
      {
        title: '门店名称',
        dataIndex: 'shopName',
      },
      { title: '门店类型', dataIndex: 'jhType', valueEnum: { ABNORMAL: '不合格门店', RECHECK: '复检门店' } },
      {
        title: '任务状态',
        dataIndex: 'taskStatus',
        valueEnum: {
          WAITING_START: '待开始',
          TRANSFER_AUDIT: '任务转办审核中',
          COMPLETED: '已完成',
          CANCELED: '已作废',
          EXPIRED: '已逾期',
        },
      },
      {
        title: '点评人',
        dataIndex: 'taskUserName',
      },
      {
        title: '报告提交时间',
        dataIndex: 'reportSubmitTime',
        render: (_, { reportSubmitTime }) =>
          reportSubmitTime ? dayjs(reportSubmitTime).format('YYYY-MM-DD HH:mm:ss') : '-',
      },
      {
        title: '任务过期时间',
        dataIndex: 'expiredTime',
        render: (_, { expiredTime }) => (expiredTime ? dayjs(expiredTime).format('YYYY-MM-DD HH:mm:ss') : '-'),
      },
      {
        title: '点评开始时间',
        dataIndex: 'taskStartTime',
        render: (_, { taskStartTime }) => (taskStartTime ? dayjs(taskStartTime).format('YYYY-MM-DD HH:mm:ss') : '-'),
      },
      {
        title: '检查表',
        dataIndex: 'worksheetNameList',
        render: (_, { worksheetNameList }) => worksheetNameList?.join(','),
      },
      {
        title: '第一点评状态',
        dataIndex: 'reviewStatus',
        valueEnum: {
          REVIEWED: '已点评',
          NOT_REVIEWED: '待点评',
          REVIEWED_EXPIRE: '点评超时',
          NO_NEEDED_REVIEWS: '无需点评',
        },
      },
      {
        title: '第一点评是否通过',
        dataIndex: 'reviewPassed',
        render: (_, { reviewPassed, reviewStatus }) => {
          if (
            [InspectionReviewStatus.NO_NEEDED_REVIEWS, InspectionReviewStatus.REVIEWED_EXPIRE].includes(reviewStatus)
          ) {
            return '-';
          }

          if (!isBoolean(reviewPassed)) {
            return '-';
          }

          if (reviewPassed) {
            return '通过';
          }

          return '不通过';
        },
      },
      {
        title: '点评提交时间',
        dataIndex: 'reviewSubmitTime',
        render: (_, { reviewSubmitTime }) =>
          reviewSubmitTime ? dayjs(reviewSubmitTime).format('YYYY-MM-DD HH:mm:ss') : '-',
      },

      {
        title: '点评时长',
        dataIndex: 'reviewDuration',
        render: (_, { reviewDuration }) => {
          return reviewDuration ? `${Math.floor(reviewDuration / 1000)}s` : '-';
        },
      },
      // {
      //   title: '第一点评时间',
      //   dataIndex: 'reviewTime',
      //   render: (_, { reviewTime }) => (reviewTime ? dayjs(reviewTime).format('YYYY-MM-DD HH:mm:ss') : '-'),
      // },
      {
        title: '操作',
        fixed: 'right',
        render: (_, record) => {
          return (
            <Space>
              {[StrategyTaskStatus.COMPLETED].includes(record?.taskStatus) && (
                <a
                  onClick={() => {
                    navigate(RouteKey.CounselingReview, {
                      searchParams: {
                        taskId: record?.taskId,
                        shopId: record?.shopId,
                        shopName: record?.shopName,
                        taskName: record?.taskName,
                        baseTaskId: record?.baseTaskId,
                        // 稽核点评任务列表表单数据
                        listFormValues: JSON.stringify(formatFormValue(form.getFieldsValue())),
                        hasReviewPermission: record?.hasReviewPermission,
                        parentTaskId: record?.parentId,
                        operationType: OperationType.Detail,
                        isAwaitDeal,
                      },
                    });
                  }}
                >
                  查看详情
                </a>
              )}
              {isAwaitDeal &&
                [StrategyTaskStatus.WAITING_START].includes(record?.taskStatus) &&
                record?.hasReviewPermission && (
                  <a
                    onClick={() => {
                      navigate(RouteKey.CounselingReview, {
                        searchParams: {
                          taskId: record?.taskId,
                          shopId: record?.shopId,
                          shopName: record?.shopName,
                          taskName: record?.taskName,
                          baseTaskId: record?.baseTaskId,
                          // 稽核点评任务列表表单数据
                          listFormValues: JSON.stringify(formatFormValue(form.getFieldsValue())),
                          hasReviewPermission: record?.hasReviewPermission,
                          parentTaskId: record?.parentId,
                        },
                      });
                    }}
                  >
                    点评
                  </a>
                )}
              {[StrategyTaskStatus.WAITING_START].includes(record?.taskStatus) && record?.hasTransfer && (
                <a
                  onClick={() =>
                    showModal({
                      initialValues: {
                        taskIds: [record?.taskId],
                      },
                      onOk: (values) => {
                        return executeRequest(RequestName.ApplyBatchTransfer, values).then(() => {
                          message.success('转派成功');
                          actionRef.current.reload();
                        });
                      },
                    })
                  }
                >
                  任务转派
                </a>
              )}
              {[StrategyTaskStatus.COMPLETED].includes(record?.taskStatus) && (
                <a
                  onClick={() => {
                    ConfirmedDescModal.showModal({
                      data: {
                        baseTaskId: record?.baseTaskId,
                        shopId: record?.shopId,
                        shopName: record?.shopName,
                        taskName: record?.taskName,
                        parentTaskId: record?.parentId,
                        readonly: true,
                      },
                    });
                  }}
                >
                  稽核确认详情
                </a>
              )}
            </Space>
          );
        },
      },
    ],
  });

  useEffect(() => {
    executeRequest(RequestName.GetGroupTreeList);
    executeRequest(RequestName.GetTags);

    requestChecklist();
    executeRequest(RequestName.QueryBaseTemplateList, {
      status: TemplateStatus.EFFECTIVE,
      type: 'REVIEW',
    });

    if (!isEmpty(_counseling)) {
      const { store, dateQuery, sheet, reportPassed, ...rest } = _counseling;
      const { groupId, shopIds } = store || {};

      if (_counseling?.taskUser) {
        executeRequest(RequestName.GetUserInfoList);
      }

      if (shopIds?.length) {
        executeRequest(RequestName.GetStoreList, {
          fightGroupId: groupId,
          groupType: 2,
          privilegeCode: 1,
        });
      }

      form.setFieldsValue({
        ...rest,
        store,
        dateQuery: {
          type: +dateQuery?.type || DateQueryType.TaskSubmitTime,
          date:
            dateQuery?.date?.[0] && dateQuery?.date?.[1]
              ? [dayjs(dateQuery.date[0]), dayjs(dateQuery.date[1])]
              : [dayjs().subtract(4, 'days').startOf('day'), dayjs().add(2, 'days').endOf('day')],
        },
        sheet: {
          tags: sheet?.tags?.map((item) => +item),
          checklist: sheet?.checklist ? +sheet?.checklist : undefined,
        },
        reportPassed: reportPassed ? reportPassed?.toString() : undefined,
      });

      /* setPaginationParams({
        pageNo: rest?.pageNo || 1,
        pageSize: rest?.pageSize || 10,
      }); */
    }

    form.submit();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <ProTable
        columns={columns}
        rowKey="taskId"
        scroll={{ x: 2500 }}
        search={{ form, collapsed: false, collapseRender: false, labelWidth: 100 }}
        params={pagination}
        request={async (value) => {
          const { current, pageSize, ...val } = value;
          const payload = {
            ...formatFormValue(val),
            pageNo: current || _counseling?.current || 1,
            pageSize: pageSize || _counseling?.pageSize || 10,
            ...pagination,
          };

          setUrlParams({
            ...urlParams,
            counseling: {
              ...val,
              pageNo: current || _counseling?.current || 1,
              pageSize: pageSize || _counseling?.pageSize || 10,
              ...pagination,
            },
          });

          setPagination({
            pageNo: current || _counseling?.current || 1,
            pageSize: pageSize || _counseling?.pageSize || 10,
            ...pagination,
          });

          const res = await executeRequest(
            isAwaitDeal
              ? RequestName.GetArriveShopTutorshipAwaitReviewTaskList
              : RequestName.GetArriveShopTutorshipReviewTaskList,
            payload,
          );

          return {
            data: res?.data,
            success: true,
            total: res?.total,
          };
        }}
        actionRef={actionRef}
        manualRequest={true}
        options={false}
        /* pagination={{
          showSizeChanger: true,
          current: paginationParams.pageNo,
          pageSize: paginationParams.pageSize,
        }}
        onChange={({ current, pageSize }) => {
          setPaginationParams((p) => ({
            ...p,
            pageNo: current ?? 1,
            pageSize: pageSize ?? 10,
          }));
        }} */
        pagination={false}
        onSubmit={() => {
          setPagination((p) => ({ ...p, pageNo: 1 }));
        }}
        onReset={() => {
          setPagination((p) => ({ ...p, pageNo: 1 }));

          actionRef?.current?.reload();
        }}
        tableRender={(props: any, _d, { table }) => {
          return (
            <ProCard>
              <div className="mb-2 flex justify-between">
                <Space>
                  {!isAwaitDeal && (
                    <Permission permission={Auth.任务中心_我的任务_食安稽核到店辅导点评任务_修改执行人}>
                      <Button
                        type="primary"
                        disabled={selectKeys.length === 0}
                        onClick={() => {
                          ModifyTaskUserModal.showModal({
                            data: {
                              taskIds: selectKeys,
                            },
                            onSuccess: () => {
                              actionRef.current.clearSelected();
                              actionRef.current.reload();
                            },
                          });
                        }}
                      >
                        修改执行人
                      </Button>
                    </Permission>
                  )}
                  <Button
                    disabled={selectKeys.length === 0}
                    onClick={() => {
                      const transferTasksIds = selectedRows
                        .filter((item) => item?.hasTransfer)
                        ?.map((item) => item?.taskId);

                      if (transferTasksIds.length === 0) {
                        message.warning('选择的任务都无法进行转派');

                        return;
                      }

                      showModal({
                        initialValues: {
                          taskIds: transferTasksIds,
                        },
                        onOk: (values) => {
                          return executeRequest(RequestName.ApplyBatchTransfer, values).then(() => {
                            message.success('转派成功');
                            actionRef.current.clearSelected();
                            actionRef.current.reload();
                          });
                        },
                      });
                    }}
                  >
                    批量转派
                  </Button>
                </Space>
                {!isAwaitDeal && (
                  <DownloadButton
                    downloadReq={() => {
                      const values = form.getFieldsValue();

                      return executeRequest(
                        RequestName.ExportArriveShopTutorshipReviewTaskList,
                        formatFormValue(values),
                      );
                    }}
                  />
                )}
              </div>
              {table}
              <PreviousPageAndNextPageButton
                pageNum={pagination.pageNo}
                pageSize={pagination.pageSize}
                dataLength={props?.action?.dataSource?.length ?? 0}
                loading={props?.action?.loading}
                onChange={({ pageNum, pageSize }) => {
                  setPagination((p) => ({
                    ...p,
                    pageNo: pageNum,
                    pageSize,
                  }));
                }}
              />
            </ProCard>
          );
        }}
        rowSelection={{
          selectedRowKeys: selectKeys,
          type: 'checkbox',
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectKeys(selectedRowKeys);
            setSelectedRows(selectedRows);
          },
          getCheckboxProps: (record) => ({
            disabled: ![StrategyTaskStatus.WAITING_START].includes(record?.taskStatus),
          }),
        }}
      />
      <ModifyTaskUserModal />
      <ConfirmedDescModal />
    </>
  );
};

export default Counseling;
