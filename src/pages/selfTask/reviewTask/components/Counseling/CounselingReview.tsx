import { useMemo, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import { Button, message, Spin } from 'antd';
import { Confirmed } from './components/Confirmed';
import { IPortal } from './components/IPortal';
import { StudyInfo } from './components/StudyInfo';
import { TaskDetail } from './components/TaskDetail';
import { TaskReview } from './components/TaskReview';
import Service, { RequestName } from '../../service';
import { NotFilledItemHandleType } from '@/constants/strategy';
import { TutorArriveShopType } from '@/constants/task';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import {
  confirmArriveShopTutorshipReviewTask,
  getNextArriveShopTutorshipAwaitReviewTaskId,
  getNextArriveShopTutorshipReviewTaskId,
} from '@/http/apis/template';
import {
  useVideoInfoContext,
  VideoInfoContextProvider,
} from '@/pages/taskCenter/cloud/patrol/context/cloud-patrol-context';
import VideoArea from '@/pages/taskCenter/cloud/patrol/videoArea';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { parserParams } from '@/utils/convert';

export default function CounselingCounselingReviewIndex() {
  const [urlParams]: any = useQuerySearchParams();
  const _urlParams = parserParams(urlParams || {});

  const { taskId, shopId, shopName, hikvisionStoreId } = _urlParams;

  return (
    <VideoInfoContextProvider shopList={[{ taskId, shopId, shopName, hikvisionStoreId }]}>
      <CounselingCounselingReview />
    </VideoInfoContextProvider>
  );
}

// 左侧区域的操作类型
export enum OperationType {
  // 确认考试情况
  Confirmed,
  // 点评
  Review,
  // 预览报告
  PreviewReport,
  // 查看详情
  Detail,
}

const CounselingCounselingReview = () => {
  const [urlParams, setUrlParams]: any = useQuerySearchParams();
  const _urlParams = parserParams(urlParams || {});
  const [operationType, setOperationType] = useState<OperationType>(
    _urlParams?.operationType || OperationType.Confirmed,
  );

  const [reviewData, setReviewData] = useState<any>({
    reviewSumUp: undefined,
    notFilledItemHandleType: undefined,
  });
  const [submitLoading, setSubmitLoading] = useState(false);

  const { infoList } = useVideoInfoContext();
  const cardHeight = useMemo(() => Math.max(document.body.clientHeight - 208, 500), []);

  const confirmedRef = useRef<any>(null);
  const navigate = useGlobalNavigate();

  const [service, executeRequest] = Service();

  const nextReviewTaskIdFn = _urlParams?.isAwaitDeal
    ? getNextArriveShopTutorshipAwaitReviewTaskId
    : getNextArriveShopTutorshipReviewTaskId;

  const { run: confirmArriveShopTutorshipReviewTaskRun, loading: confirmArriveShopTutorshipReviewTaskLoading } =
    useRequest(confirmArriveShopTutorshipReviewTask, {
      manual: true,
      onSuccess: (_, params) => {
        if (params?.[0]?.onlineArriveShopRectifyType === TutorArriveShopType.NOT_ARRIVED) {
          // 如果选择未到店，则不进入详情页面
          getNextReviewTaskId(_urlParams?.listFormValues);

          return;
        }

        setOperationType(OperationType.Review);
      },
    });

  const { run: getNextReviewTaskId, loading: getNextReviewTaskIdLoading } = useRequest(nextReviewTaskIdFn, {
    manual: true,
    onSuccess: (res: any) => {
      if (!res) {
        message.warning('没有下一个点评任务');

        navigate(-1);

        return;
      }

      resetState();

      setUrlParams(
        {
          taskId: res?.taskId,
          shopId: res?.shopId,
          shopName: res?.shopName,
          taskName: res?.taskName,
          baseTaskId: res?.baseTaskId,
          // 稽核点评任务列表表单数据
          listFormValues: JSON.stringify(_urlParams?.listFormValues),
          // 默认都有权限
          hasReviewPermission: true,
          parentTaskId: res?.parentId,
        },
        {
          replace: true,
        },
      );
    },
  });

  // 重置状态
  const resetState = () => {
    setOperationType(OperationType.Confirmed);
    setReviewData({
      reviewSumUp: undefined,
      notFilledItemHandleType: undefined,
    });
  };

  const renderLeftPart = () => {
    if (operationType === OperationType.Review) {
      return (
        <TaskReview
          taskId={_urlParams?.baseTaskId}
          onPreview={({ reviewSumUp, notFilledItemHandleType }) => {
            setReviewData({
              reviewSumUp,
              notFilledItemHandleType,
            });

            setOperationType(OperationType.PreviewReport);
          }}
          needReviewSummary={true}
          hasReview={_urlParams?.hasReviewPermission}
          saveReviewItem={(dto) => {
            return executeRequest(RequestName.SaveReviewItem, dto);
          }}
          reviewSumUp={reviewData?.reviewSumUp}
        />
      );
    }

    if (operationType === OperationType.PreviewReport || operationType === OperationType.Detail) {
      return (
        <Spin spinning={submitLoading}>
          <TaskDetail
            taskId={_urlParams?.baseTaskId}
            needReviewSummary={true}
            needStatistics={true}
            reviewSumUp={reviewData?.reviewSumUp}
            notFilledItemHandleType={reviewData?.notFilledItemHandleType}
            footer={() => {
              return (
                <IPortal attach=".CounselingReview-bottom">
                  <div className="flex gap-3">
                    <Button
                      onClick={async () => {
                        try {
                          setSubmitLoading(true);

                          if (reviewData?.reviewSumUp) {
                            await executeRequest(RequestName.SubmitReviewSummary, {
                              taskId: _urlParams?.baseTaskId,
                              summary: reviewData?.reviewSumUp,
                            });
                          }

                          await executeRequest(RequestName.SubmitReviewTask, {
                            baseTaskId: _urlParams?.baseTaskId,
                            notFilledItemHandleType:
                              reviewData?.notFilledItemHandleType || NotFilledItemHandleType.SET_FULL_SCORE,
                          });

                          const res = (await nextReviewTaskIdFn(_urlParams?.listFormValues)) as any;

                          if (!res) {
                            message.warning('没有下一个点评任务');

                            navigate(-1);

                            setSubmitLoading(false);

                            return;
                          }

                          resetState();

                          setUrlParams(
                            {
                              taskId: res?.taskId,
                              shopId: res?.shopId,
                              shopName: res?.shopName,
                              taskName: res?.taskName,
                              baseTaskId: res?.baseTaskId,
                              // 稽核点评任务列表表单数据
                              listFormValues: JSON.stringify(_urlParams?.listFormValues),
                              // 默认都有权限
                              hasReviewPermission: true,
                              parentTaskId: res?.parentId,
                            },
                            {
                              replace: true,
                            },
                          );

                          setSubmitLoading(false);
                        } catch (error) {
                          message.error('提交失败');

                          setSubmitLoading(false);
                        }
                      }}
                    >
                      提交并继续审核
                    </Button>
                    <Button
                      type="primary"
                      onClick={async () => {
                        try {
                          setSubmitLoading(true);

                          if (reviewData?.reviewSumUp) {
                            await executeRequest(RequestName.SubmitReviewSummary, {
                              taskId: _urlParams?.baseTaskId,
                              summary: reviewData?.reviewSumUp,
                            });
                          }

                          await executeRequest(RequestName.SubmitReviewTask, {
                            baseTaskId: _urlParams?.baseTaskId,
                            notFilledItemHandleType:
                              reviewData?.notFilledItemHandleType || NotFilledItemHandleType.SET_FULL_SCORE,
                          });

                          navigate(-1);

                          setSubmitLoading(false);
                        } catch (error) {
                          setSubmitLoading(false);
                        }
                      }}
                    >
                      提交
                    </Button>
                  </div>
                </IPortal>
              );
            }}
          />
        </Spin>
      );
    }

    return (
      <>
        <StudyInfo {..._urlParams} />
        <Confirmed className="mt-4" ref={confirmedRef} parentTaskId={_urlParams?.parentTaskId} />
      </>
    );
  };

  const renderBottom = () => {
    if (operationType === OperationType.Detail) {
      return null;
    }

    if (operationType === OperationType.Review || operationType === OperationType.PreviewReport) {
      return (
        <div
          style={{
            borderTop: '1px solid #E5E5E5',
          }}
          className="flex justify-between p-4"
        >
          <Button
            onClick={() => {
              if (operationType === OperationType.PreviewReport) {
                setOperationType(OperationType.Review);
              }

              if (operationType === OperationType.Review) {
                setOperationType(OperationType.Confirmed);
              }
            }}
          >
            上一步
          </Button>
          <div className="CounselingReview-bottom" />
        </div>
      );
    }

    return (
      <div
        style={{
          borderTop: '1px solid #E5E5E5',
        }}
        className="flex justify-end p-4"
      >
        <Button
          type="primary"
          onClick={async () => {
            const formValues = await confirmedRef.current?.values();

            confirmArriveShopTutorshipReviewTaskRun({
              ...formValues,
              parentTaskId: _urlParams?.parentTaskId,
            });
          }}
          loading={getNextReviewTaskIdLoading || confirmArriveShopTutorshipReviewTaskLoading}
        >
          下一步
        </Button>
      </div>
    );
  };

  return (
    <div className="flex" style={{ height: cardHeight }}>
      <div className="bg-white rounded-md max-h-full flex flex-col h-full w-[400px]">
        <div className="grow overflow-y-auto p-4">{renderLeftPart()}</div>
        {renderBottom()}
      </div>
      <div className="flex-1 w-0" id="videoArea">
        {infoList?.length > 0 && <VideoArea />}
      </div>
    </div>
  );
};
