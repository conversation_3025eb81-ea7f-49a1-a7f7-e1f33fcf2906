import { useEffect, useRef, useState } from 'react';
import { ProCard, ProForm, ProFormInstance, ProFormSelect, ProTable } from '@ant-design/pro-components';
import { Button } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { isEmpty } from 'lodash';
import { eventTypeOption, eventTypeValueMap, eventTypeVlaueOptionForAll } from './contanst';
import Service, { RequestName } from './service';
import PreviousPageAndNextPageButton from '@/components/PreviousPageAndNextPage/Button';
import { getColumns, getCommonConfig } from '@/components/pro-table-config';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { layoutStore } from '@/mobx';
import { RouteKey } from '@/router/config';
import useGlobalNavigate from '@/router/useGlobalNavigate';
import { parserParams } from '@/utils/convert';
import { formatDateToUTC } from '@/utils/date';
import { renderPage } from '@/utils/render';

const EventLogIndex = renderPage(() => {
  const [form] = ProForm.useForm();
  const formRef = useRef<ProFormInstance>();
  const [service, executeRequest] = Service();
  const navigate = useGlobalNavigate();

  const [urlParams, setUrlParams] = useQuerySearchParams();
  const _eventLog = parserParams(urlParams?.eventLog || {});

  const isFirstSubmintRef = useRef(true);

  const [paginationParams, setPaginationParams] = useState({
    page: _eventLog?.page || 1,
    size: _eventLog?.size || 10,
  });

  const _eventType = ProForm.useWatch('eventType', form);

  const columns = getColumns({
    searchColumns: [
      {
        title: '事件类型',
        dataIndex: 'eventType',
        valueType: 'select',
        fieldProps: {
          options: eventTypeOption,
          onChange: () => {
            // 当事件类型改变时，清空事件值
            form.setFieldValue('eventValue', undefined);
          },
        },
      },
      {
        dataIndex: 'eventValue',
        hideInTable: true,
        colSize: 1.5,

        renderFormItem: () => {
          // 根据事件类型决定标题和选项
          let options = [];

          if (_eventType === 'PROGRESS_OF_WORKS') {
            options = eventTypeValueMap.PROGRESS_OF_WORKS || [];
          } else if (_eventType === 'RESIGNATION_HANDOVER') {
            options = eventTypeValueMap.RESIGNATION_HANDOVER || [];
          }

          return (
            <ProFormSelect
              name="eventValue"
              label="事件名称"
              options={options}
              placeholder={`请选择事件名称`}
              allowClear
              disabled={!_eventType}
            />
          );
        },
      },
      {
        title: '触发时间',
        dataIndex: 'eventTime',
        valueType: 'dateRange',
        colSize: 1.5,
        initialValue: [dayjs().subtract(1, 'month').startOf('day'), dayjs().endOf('day')],
        search: {
          transform: (value: any) => {
            return {
              startTime: formatDateToUTC(dayjs(value[0]).startOf('day')),
              endTime: formatDateToUTC(dayjs(value[1]).endOf('day')),
            };
          },
        },
        fieldProps: {
          allowClear: false,
          disabledDate: (current: Dayjs, { from }) => {
            if (from) {
              return Math.abs(current.diff(from, 'month')) >= 3;
            }

            return false;
          },
        },
      },
    ],
    tableColumns: [
      { title: 'ID', dataIndex: 'id' },
      {
        title: '事件类型',
        dataIndex: 'eventType',
        valueType: 'select',
        fieldProps: {
          options: eventTypeOption,
        },
      },
      {
        title: '事件名称',
        dataIndex: 'eventValue',
        valueType: 'select',
        fieldProps: {
          options: eventTypeVlaueOptionForAll,
        },
      },
      {
        title: '触发时间',
        dataIndex: 'createdAt',
        valueType: 'dateTime',
      },
      {
        title: '操作',
        valueType: 'option',
        render: (_, record) => {
          return (
            <a
              onClick={() => {
                navigate(RouteKey.EventLogDetail, {
                  searchParams: {
                    eventId: record?.id,
                    eventType:
                      eventTypeOption?.find((item) => item.value === record?.eventType)?.label || record?.eventType,
                    eventValue:
                      eventTypeVlaueOptionForAll?.find((item) => item.value === record?.eventValue)?.label ||
                      record?.eventValue,
                  },
                });
              }}
            >
              事件详情
            </a>
          );
        },
      },
    ],
  });

  useEffect(() => {
    if (!isEmpty(_eventLog)) {
      const { startTime, endTime, ...rest } = _eventLog;

      form.setFieldsValue({
        ...rest,
        eventTime: startTime && endTime ? [startTime, endTime] : undefined,
      });
    }

    form.submit();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ProTable
      rowKey="id"
      {...getCommonConfig({
        search: {
          form,
        },
        manualRequest: true,
      })}
      formRef={formRef}
      options={false}
      columns={columns}
      request={async (values) => {
        const { current, size, ...val } = values;

        const payload = {
          ...val,
          page: paginationParams.page,
          size: paginationParams.size,
        };

        // 同步参数到url
        setUrlParams({
          ...urlParams,
          eventLog: payload,
        });

        const res = await executeRequest(RequestName.GetEventsList, payload);

        return {
          data: res?.data || [],
          success: true,
          total: res?.total,
        };
      }}
      tableRender={(props: any, _d, { table }) => {
        return (
          <ProCard
            extra={
              <Button
                type="primary"
                onClick={() => {
                  executeRequest(RequestName.ExportEventsList, formRef.current?.getFieldsFormatValue()).then(() => {
                    layoutStore.increaseExport();
                  });
                }}
              >
                下载明细
              </Button>
            }
          >
            {table}
            <PreviousPageAndNextPageButton
              pageNum={paginationParams.page}
              pageSize={paginationParams.size}
              dataLength={props?.action?.dataSource?.length ?? 0}
              loading={props?.action?.loading}
              onChange={({ pageNum, pageSize }) => {
                setPaginationParams((p) => ({
                  ...p,
                  page: pageNum,
                  size: pageSize,
                }));
              }}
            />
          </ProCard>
        );
      }}
      pagination={false}
      params={paginationParams}
      onSubmit={() => {
        if (isFirstSubmintRef.current) {
          isFirstSubmintRef.current = false;

          return;
        }

        setPaginationParams((p) => ({
          ...p,
          page: 1,
        }));
      }}
      onReset={() => {
        setPaginationParams((p) => ({
          ...p,
          page: 1,
        }));
      }}
    />
  );
});

export default EventLogIndex;
