import { useRef, useState } from 'react';
import ProCard from '@ant-design/pro-card';
import ProTable from '@ant-design/pro-table';
import { Descriptions } from 'antd';
import Service, { RequestName } from '../service';
import PreviousPageAndNextPageButton from '@/components/PreviousPageAndNextPage/Button';
import { getColumns } from '@/components/pro-table-config';
import { StrategyTemplateTypeCN } from '@/constants/strategy';
import useQuerySearchParams from '@/hooks/use-query-search-params';
import { parserParams } from '@/utils/convert';

export default function DetailIndex() {
  const [urlParams, setUrlParams] = useQuerySearchParams();
  const _urlParams = parserParams(urlParams || {});
  const [service, executeRequest] = Service();

  const [paginationParams, setPaginationParams] = useState({
    page: _urlParams?.page || 1,
    size: _urlParams?.size || 10,
  });
  const isFirstSubmintRef = useRef(true);

  const columns = getColumns({
    tableColumns: [
      {
        title: '任务id',
        dataIndex: 'id',
      },
      {
        title: '任务类型',
        dataIndex: 'taskType',
        valueEnum: StrategyTemplateTypeCN,
      },
      {
        title: '任务名称',
        dataIndex: 'taskName',
      },
      {
        title: '门店编号',
        dataIndex: ['shop', 'shopId'],
      },
      {
        title: '门店名称',
        dataIndex: ['shop', 'shopName'],
      },
      {
        title: '执行人',
        dataIndex: ['taskUser', 'nickname'],
      },
      {
        title: '开始时间',
        dataIndex: 'begin',
        valueType: 'dateTime',
      },
      {
        title: '结束时间',
        dataIndex: 'expiredTime',
        valueType: 'dateTime',
      },
    ],
  });

  return (
    <>
      <ProCard>
        <Descriptions>
          <Descriptions.Item label="事件类型">{_urlParams?.eventType}</Descriptions.Item>
          <Descriptions.Item label="事件名称">{_urlParams?.eventValue}</Descriptions.Item>
        </Descriptions>
      </ProCard>
      <ProTable
        className="mt-4"
        rowKey="id"
        search={false}
        options={false}
        pagination={false}
        tableRender={(props: any, _d, { table }) => {
          return (
            <ProCard>
              {table}
              <PreviousPageAndNextPageButton
                pageNum={paginationParams.page}
                pageSize={paginationParams.size}
                dataLength={props?.action?.dataSource?.length ?? 0}
                loading={props?.action?.loading}
                onChange={({ pageNum, pageSize }) => {
                  setPaginationParams((p) => ({
                    ...p,
                    page: pageNum,
                    size: pageSize,
                  }));
                }}
              />
            </ProCard>
          );
        }}
        columns={columns}
        request={async () => {
          // 同步参数到url
          setUrlParams({
            ...urlParams,
            page: paginationParams.page,
            size: paginationParams.size,
          });

          const res = await executeRequest(RequestName.GetEventsTasks, {
            eventId: _urlParams?.eventId,
            page: paginationParams.page,
            size: paginationParams.size,
          });

          return {
            data: res?.data || [],
            success: true,
            total: res?.total,
          };
        }}
        params={paginationParams}
        onSubmit={() => {
          if (isFirstSubmintRef.current) {
            isFirstSubmintRef.current = false;

            return;
          }

          setPaginationParams((p) => ({
            ...p,
            page: 1,
          }));
        }}
        onReset={() => {
          setPaginationParams((p) => ({
            ...p,
            page: 1,
          }));
        }}
      />
    </>
  );
}
