import useServiceReducer from '@/hooks/use-service-reducer';
import { exportEventsList, getEventsList, getEventsTasks } from '@/http/apis/strategy';

export enum RequestName {
  GetEventsList,
  GetEventsTasks,
  ExportEventsList,
}

export const initState: any = {};

const Service = () => {
  const [service, executeRequest, dispatch] = useServiceReducer(initState, {
    [RequestName.GetEventsList]: {
      request: getEventsList,
    },
    [RequestName.GetEventsTasks]: {
      request: getEventsTasks,
    },
    [RequestName.ExportEventsList]: {
      request: exportEventsList,
    },
  });

  return [service, executeRequest, dispatch];
};

export default Service;
