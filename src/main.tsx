import { isInIcestark, setLibraryName } from '@ice/stark-app';
import { init as sentryInit } from '@sentry/react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './app';

if (import.meta.env.PROD) {
  sentryInit({
    dsn: import.meta.env.VITE_SENTRY_DSN,
    // 用户操作重放的采样率
    replaysSessionSampleRate: 0.1,

    // 当报错时，重放的采样率
    replaysOnErrorSampleRate: 1.0,
    // 性能跟踪的采样率
    tracesSampleRate: 0.1,
    environment: import.meta.env.MODE,
  });
}

export function mount(props: any) {
  ReactDOM.createRoot(props?.container).render(<App />);
}

export function unmount(props: any) {
  ReactDOM.createRoot(props?.container).unmount();
}

setLibraryName('gztApp');

if (!isInIcestark()) {
  ReactDOM.createRoot(document.getElementById('root')!).render(<App />);
}
