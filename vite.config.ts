import path, { resolve } from 'path';
import federation from '@originjs/vite-plugin-federation';
import { sentryVitePlugin } from '@sentry/vite-plugin';
import react from '@vitejs/plugin-react';
import { defineConfig, loadEnv } from 'vite';
import htmlPlugin from 'vite-plugin-index-html';
import { viteMockServe } from 'vite-plugin-mock';
import { dependencies } from './package.json';

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd(), '');

  const omLibraryUrl = env.VITE_OM_LIBRARY_URL;

  const plugins = [
    react(),
    htmlPlugin({
      input: resolve(__dirname, './src/main.tsx'),
      preserveEntrySignatures: 'exports-only',
    }),

    viteMockServe({
      mockPath: 'mock',
      enable: mode === 'mock',
    }),
    federation({
      name: 'om',
      remotes: {
        om_remote_library: omLibraryUrl,
      },
      remoteType: 'module',
      shared: {
        react: {
          requiredVersion: dependencies.react,
          modulePreload: true,
        },
        'react-dom': {
          requiredVersion: dependencies['react-dom'],
          modulePreload: true,
        },
        'react-router-dom': {
          requiredVersion: dependencies['react-router-dom'],
          modulePreload: true,
        },
        '@ant-design/icons': {
          requiredVersion: dependencies['@ant-design/icons'],
          modulePreload: true,
        },
        antd: {
          requiredVersion: dependencies['antd'],
          modulePreload: true,
        },
        '@ant-design/pro-components': {
          requiredVersion: dependencies['@ant-design/pro-components'],
          modulePreload: true,
        },
        dayjs: {
          requiredVersion: dependencies['dayjs'],
          modulePreload: true,
        },
      },
    }),

    // VitePluginImp({
    //   libList: [{ libName: 'antd', style: (name) => `antd/es/${name}/style` }]
    // })
  ];

  if (mode === 'production') {
    plugins.push(
      sentryVitePlugin({
        org: 'tastien',
        project: 'om-manage',
        url: 'https://sentry2.tastientech.com/',
        authToken: env.SENTRY_AUTH_TOKEN,
      }),
    );
  }

  return {
    server: {
      // 指定dev sever的端口号，默认为5173
      port: 3000,
      // 自动打开浏览器运行以下页面
      host: true,
      open: '/',
      cors: true,
      proxy: {
        '/cc-api': {
          target: env.CC_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/cc-api/, '/api'),
        },
        '/om-api': {
          target: env.OM_URL,
          // target: 'http://***********:8080/',
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/om-api/, '/api')
        },
        '/rm-api': {
          target: env.OM_URL,
          // target: 'http://***********:8080/',
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/om-api/, '/api')
        },
        '/tm-api': {
          target: env.OM_URL,
          // target: 'http://************:8080/',
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/om-api/, '/api')
        },
      },
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    build: {
      sourcemap: true,
      target: 'es2022',
    },
    plugins,
    base: env.VITE_BASE_URL,
  };
});
